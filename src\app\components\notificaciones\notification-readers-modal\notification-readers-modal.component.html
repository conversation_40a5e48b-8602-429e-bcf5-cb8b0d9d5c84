<div class="notification-readers-modal">
  <!-- Header del modal -->
  <div class="modal-header flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
    <div>
      <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
        Usuarios que han leído la notificación
      </h2>
      <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
        {{ data.notificationTitle }}
      </p>
    </div>
    <button
      (click)="onClose()"
      class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>
  </div>

  <!-- Contenido del modal -->
  <div class="modal-content p-6">
    <!-- Estado de carga -->
    <div *ngIf="loading" class="flex flex-col items-center justify-center py-12">
      <svg
        class="animate-spin h-10 w-10 text-indigo-600 dark:text-indigo-400 mb-4"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      <p class="text-gray-600 dark:text-gray-300">Cargando usuarios...</p>
    </div>

    <!-- Estado de error -->
    <div *ngIf="error && !loading" class="flex flex-col items-center justify-center py-12">
      <svg
        class="h-12 w-12 text-red-500 mb-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        ></path>
      </svg>
      <p class="text-gray-600 dark:text-gray-300 mb-4">Error al cargar los usuarios</p>
      <button
        (click)="retry()"
        class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
      >
        Reintentar
      </button>
    </div>

    <!-- Lista de usuarios -->
    <div *ngIf="!loading && !error" class="space-y-3">
      <!-- Contador -->
      <div class="text-sm text-gray-500 dark:text-gray-400 mb-4">
        {{ readers.length }} {{ readers.length === 1 ? 'persona ha leído' : 'personas han leído' }} esta notificación
      </div>

      <!-- Lista vacía -->
      <div *ngIf="readers.length === 0" class="text-center py-8">
        <svg
          class="h-12 w-12 text-gray-400 mx-auto mb-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          ></path>
        </svg>
        <p class="text-gray-500 dark:text-gray-400">Aún no hay lecturas de esta notificación</p>
      </div>

      <!-- Lista de usuarios que han leído -->
      <div *ngFor="let reader of readers" class="flex items-center space-x-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <!-- Avatar -->
        <div class="flex-shrink-0">
          <img
            [src]="getAvatarUrl(reader)"
            [alt]="getNombreCompleto(reader)"
            class="h-10 w-10 rounded-full object-cover"
          />
        </div>

        <!-- Información del usuario -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
              {{ getNombreCompleto(reader) }}
            </p>
            <span class="text-xs text-gray-500 dark:text-gray-400">
              ({{ reader.userName }})
            </span>
          </div>
          
          <div class="flex items-center space-x-4 mt-1">
            <!-- Sede -->
            <div class="flex items-center space-x-1">
              <svg class="h-3 w-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
              <span class="text-xs text-gray-500 dark:text-gray-400">
                {{ reader.sedeNombre || 'Sin sede' }}
              </span>
            </div>

            <!-- Email -->
            <div class="flex items-center space-x-1" *ngIf="reader.userEmail">
              <svg class="h-3 w-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              <span class="text-xs text-gray-500 dark:text-gray-400 truncate">
                {{ reader.userEmail }}
              </span>
            </div>
          </div>
        </div>

        <!-- Tiempo de lectura -->
        <div class="flex-shrink-0 text-right">
          <div class="flex items-center space-x-1">
            <svg class="h-3 w-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-xs text-gray-500 dark:text-gray-400">
              {{ formatReadTime(reader.readAt) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer del modal -->
  <div class="modal-footer flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
    <button
      (click)="onClose()"
      class="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
    >
      Cerrar
    </button>
  </div>
</div>
