<div class="notification-readers flex items-center space-x-2">
  <!-- Avatares de usuarios que han leído -->
  <div class="flex -space-x-2 overflow-hidden" *ngIf="readers.length > 0">
    <div
      *ngFor="let reader of displayedReaders; trackBy: trackByUserId"
      class="inline-block h-8 w-8 rounded-full ring-2 ring-white dark:ring-gray-800 hover:z-10 transition-transform hover:scale-110 cursor-pointer"
      [title]="reader.userName + ' - ' + formatReadTime(reader.readAt)"
    >
      <img
        [src]="getAvatarUrl(reader)"
        [alt]="reader.userName"
        class="h-8 w-8 rounded-full object-cover"
        (error)="onImageError($event, reader)"
      />
    </div>

    <!-- Indicador de usuarios adicionales -->
    <div
      *ngIf="remainingCount > 0"
      class="flex h-8 w-8 items-center justify-center rounded-full bg-gray-400 dark:bg-gray-600 text-xs font-medium text-white ring-2 ring-white dark:ring-gray-800"
      [title]="'+' + remainingCount + ' más'"
    >
      +{{ remainingCount }}
    </div>
  </div>

  <!-- Contador de lecturas clickeable -->
  <div
    *ngIf="showCount && readCount > 0"
    class="text-xs text-gray-500 dark:text-gray-400 ml-2"
  >
    <button
      (click)="openReadersModal()"
      class="flex items-center hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors duration-200 cursor-pointer group"
      title="Ver quién ha leído esta notificación"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-3 w-3 mr-1 group-hover:scale-110 transition-transform duration-200"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
        />
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
        />
      </svg>
      <span class="group-hover:underline">
        {{ readCount }}
        {{ readCount === 1 ? "persona ha leído" : "personas han leído" }}
      </span>
    </button>
  </div>

  <!-- Estado de carga -->
  <div *ngIf="loading" class="flex items-center">
    <svg
      class="animate-spin h-4 w-4 text-gray-400"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        class="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        stroke-width="4"
      ></circle>
      <path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
  </div>

  <!-- Mensaje cuando no hay lecturas -->
  <div
    *ngIf="!loading && readers.length === 0"
    class="text-xs text-gray-400 dark:text-gray-500"
  >
    Sin lecturas aún
  </div>
</div>
