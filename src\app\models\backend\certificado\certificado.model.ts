import { User } from '../user';
import { Curso } from '../curso/curso.model';

/**
 * Modelo que representa un certificado emitido a un usuario
 */
export interface Certificado {
  id: number;
  usuarioId: number;
  usuarioNombre: string;
  usuarioApellido: string;
  usuarioDni: string;
  usuarioEmail: string;
  cursoId: number;
  cursoNombre: string;
  cursoDescripcion: string;
  fechaEmision: string | any[];
  horasCurso: number;
  codigoCertificado: string;
  emitidoPorId: number;
  emitidoPorNombre: string;
  emitidoPorApellido: string;
  observaciones?: string;
  estado: string; // A: Activo, I: Inactivo
  fechaCreacion: string | any[];
  fechaActualizacion?: string | any[];
  certificadoUrl?: string; // URL del certificado en Firebase
}

/**
 * Modelo para crear un nuevo certificado
 */
export interface CertificadoCreateRequest {
  usuarioId: number;
  cursoId: number;
  fechaEmision?: string;
  horasCurso: number;
  observaciones?: string;
  certificadoUrl?: string;
}

/**
 * Modelo para el progreso detallado de un usuario en un curso
 */
export interface ProgresoDetallado {
  totalLecciones: number;
  leccionesCompletadas: number;
  porcentajeCompletado: number;
  puedeRecibirCertificado: boolean;
}

/**
 * Modelo para usuarios elegibles para certificado
 */
export interface UsuarioElegible {
  usuarioId: number;
  nombre: string;
  apellido: string;
  dni: string;
  email: string;
  totalLecciones: number;
  leccionesCompletadas: number;
  porcentajeCompletado: number;
  puedeRecibirCertificado: boolean;
  tieneCertificado: boolean;
}
