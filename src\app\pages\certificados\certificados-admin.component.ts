import { Component, OnInit, OnDestroy } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { PageEvent } from '@angular/material/paginator';
import { Subject } from 'rxjs';
import { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import Swal from 'sweetalert2';

import { CertificadoService } from '@app/services/certificado.service';
import { CursoService } from '@app/services/curso.service';
import { UserService } from '@app/services/user.service';
import { GenericResponse } from '@app/models/backend/generic-response';
import {
  Certificado,
  CertificadoCreateRequest,
  UsuarioElegible,
} from '@app/models/backend/certificado/certificado.model';
import { Curso } from '@app/models/backend/curso/curso.model';
import { User } from '@app/models/backend/user';

@Component({
  selector: 'app-certificados-admin',
  templateUrl: './certificados-admin.component.html',
  styleUrls: ['./certificados-admin.component.scss'],
})
export class CertificadosAdminComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Datos de la tabla
  dataSource = new MatTableDataSource<Certificado>();
  displayedColumns: string[] = [
    'codigoCertificado',
    'usuarioNombre',
    'cursoNombre',
    'fechaEmision',
    'horasCurso',
    'emitidoPorNombre',
    'estado',
    'acciones',
  ];

  // Paginación
  totalElements = 0;
  pageSize = 10;
  pageIndex = 0;
  pageSizeOptions = [5, 10, 25, 50];

  // Filtros
  searchTerm = '';
  selectedEstado = '';
  estados = [
    { value: '', label: 'Todos' },
    { value: 'A', label: 'Activo' },
    { value: 'I', label: 'Inactivo' },
  ];

  // Loading states
  loading = false;
  loadingCursos = false;

  // Datos para crear certificados
  cursos: Curso[] = [];
  usuariosElegibles: UsuarioElegible[] = [];
  selectedCurso: Curso | null = null;

  constructor(
    private certificadoService: CertificadoService,
    private cursoService: CursoService,
    private userService: UserService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.cargarCertificados();
    this.cargarCursos();
    this.setupSearchDebounce();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Configurar debounce para la búsqueda
   */
  private setupSearchDebounce(): void {
    // Implementar debounce para búsqueda si es necesario
  }

  /**
   * Cargar certificados con paginación
   */
  cargarCertificados(): void {
    this.loading = true;

    this.certificadoService
      .obtenerCertificadosPaginados(
        this.pageIndex,
        this.pageSize,
        'fechaCreacion',
        'desc',
        this.searchTerm || undefined,
        this.selectedEstado || undefined
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.dataSource.data = response.data.content || [];
            this.totalElements = response.data.totalElements || 0;
          } else {
            this.dataSource.data = [];
            this.totalElements = 0;
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('Error al cargar certificados:', error);
          Swal.fire('Error', 'No se pudieron cargar los certificados', 'error');
          this.loading = false;
        },
      });
  }

  /**
   * Cargar lista de cursos
   */
  cargarCursos(): void {
    this.loadingCursos = true;

    this.cursoService
      .getAllCursos()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: GenericResponse<Curso[]>) => {
          if (response.rpta === 1 && response.data) {
            this.cursos = response.data;
          }
          this.loadingCursos = false;
        },
        error: (error: any) => {
          console.error('Error al cargar cursos:', error);
          this.loadingCursos = false;
        },
      });
  }

  /**
   * Manejar cambio de página
   */
  onPageChange(event: PageEvent): void {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.cargarCertificados();
  }

  /**
   * Aplicar filtros
   */
  aplicarFiltros(): void {
    this.pageIndex = 0;
    this.cargarCertificados();
  }

  /**
   * Limpiar filtros
   */
  limpiarFiltros(): void {
    this.searchTerm = '';
    this.selectedEstado = '';
    this.pageIndex = 0;
    this.cargarCertificados();
  }

  /**
   * Seleccionar curso y cargar usuarios elegibles
   */
  onCursoSeleccionado(curso: Curso): void {
    this.selectedCurso = curso;
    this.cargarUsuariosElegibles(curso.id);
  }

  /**
   * Cargar usuarios elegibles para certificado
   */
  cargarUsuariosElegibles(cursoId: number): void {
    this.certificadoService
      .obtenerUsuariosElegibles(cursoId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.usuariosElegibles = response.data;
          } else {
            this.usuariosElegibles = [];
          }
        },
        error: (error) => {
          console.error('Error al cargar usuarios elegibles:', error);
          Swal.fire(
            'Error',
            'No se pudieron cargar los usuarios elegibles',
            'error'
          );
        },
      });
  }

  /**
   * Crear certificado para un usuario
   */
  crearCertificado(usuario: UsuarioElegible): void {
    if (!this.selectedCurso) {
      Swal.fire('Error', 'Debe seleccionar un curso', 'error');
      return;
    }

    if (!usuario.puedeRecibirCertificado) {
      Swal.fire('Error', 'El usuario no ha completado el curso', 'error');
      return;
    }

    if (usuario.tieneCertificado) {
      Swal.fire(
        'Error',
        'El usuario ya tiene un certificado para este curso',
        'error'
      );
      return;
    }

    Swal.fire({
      title: '¿Crear certificado?',
      text: `¿Está seguro de crear un certificado para ${usuario.nombre} ${usuario.apellido}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Sí, crear',
      cancelButtonText: 'Cancelar',
    }).then((result) => {
      if (result.isConfirmed) {
        this.procesarCreacionCertificado(usuario);
      }
    });
  }

  /**
   * Procesar la creación del certificado
   */
  private procesarCreacionCertificado(usuario: UsuarioElegible): void {
    if (!this.selectedCurso) return;

    const certificadoRequest: CertificadoCreateRequest = {
      usuarioId: usuario.usuarioId,
      cursoId: this.selectedCurso.id,
      horasCurso: this.calcularHorasCurso(this.selectedCurso),
      observaciones: `Certificado generado automáticamente por completar el curso ${this.selectedCurso.nombre}`,
    };

    Swal.fire({
      title: 'Generando certificado...',
      text: 'Por favor espere mientras se genera el certificado',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      },
    });

    this.certificadoService
      .crearCertificadoConImagen(
        certificadoRequest,
        usuario.nombre,
        usuario.apellido,
        this.selectedCurso.nombre
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1) {
            Swal.fire('Éxito', 'Certificado creado exitosamente', 'success');
            this.cargarCertificados();
            this.cargarUsuariosElegibles(this.selectedCurso!.id);
          } else {
            Swal.fire(
              'Error',
              response.msg || 'Error al crear el certificado',
              'error'
            );
          }
        },
        error: (error) => {
          console.error('Error al crear certificado:', error);
          Swal.fire('Error', 'No se pudo crear el certificado', 'error');
        },
      });
  }

  /**
   * Calcular horas del curso (estimación)
   */
  private calcularHorasCurso(curso: Curso): number {
    // Por ahora retornamos un valor por defecto
    // En el futuro se puede calcular basado en la duración de las lecciones
    return 20;
  }

  /**
   * Ver certificado
   */
  verCertificado(certificado: Certificado): void {
    if (certificado.certificadoUrl) {
      window.open(certificado.certificadoUrl, '_blank');
    } else {
      Swal.fire('Error', 'No se encontró la URL del certificado', 'error');
    }
  }

  /**
   * Descargar certificado
   */
  descargarCertificado(certificado: Certificado): void {
    this.certificadoService.descargarCertificadoPDF(certificado);
  }

  /**
   * Convertir fecha de emisión a Date
   */
  getFechaEmision(fechaEmision: string | any[]): Date | null {
    if (!fechaEmision) return null;

    if (typeof fechaEmision === 'string') {
      return new Date(fechaEmision);
    }

    if (Array.isArray(fechaEmision) && fechaEmision.length >= 3) {
      // Formato [año, mes, día, hora, minuto, segundo]
      const [year, month, day, hour = 0, minute = 0, second = 0] = fechaEmision;
      return new Date(year, month - 1, day, hour, minute, second);
    }

    return null;
  }

  /**
   * Eliminar certificado
   */
  eliminarCertificado(certificado: Certificado): void {
    Swal.fire({
      title: '¿Eliminar certificado?',
      text: `¿Está seguro de eliminar el certificado de ${certificado.usuarioNombre} ${certificado.usuarioApellido}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Sí, eliminar',
      cancelButtonText: 'Cancelar',
      confirmButtonColor: '#d33',
    }).then((result) => {
      if (result.isConfirmed) {
        this.certificadoService
          .eliminarCertificado(certificado.id)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (response) => {
              if (response.rpta === 1) {
                Swal.fire(
                  'Eliminado',
                  'Certificado eliminado exitosamente',
                  'success'
                );
                this.cargarCertificados();
              } else {
                Swal.fire(
                  'Error',
                  response.msg || 'Error al eliminar el certificado',
                  'error'
                );
              }
            },
            error: (error) => {
              console.error('Error al eliminar certificado:', error);
              Swal.fire('Error', 'No se pudo eliminar el certificado', 'error');
            },
          });
      }
    });
  }
}
