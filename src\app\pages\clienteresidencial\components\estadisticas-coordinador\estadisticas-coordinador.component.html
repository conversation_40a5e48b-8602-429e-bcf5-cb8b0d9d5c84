<div class="dark:bg-gray-900 shadow-md rounded-2xl p-6 sm:p-8 transition-all">
  <div
    class="mb-3"
    *ngIf="
      fechaFinControl.value &&
      fechaFinControl.value !== fechaInicioControl.value
    "
  >
    <div
      class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3"
    >
      <div class="flex items-center">
        <svg
          class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fill-rule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
            clip-rule="evenodd"
          ></path>
        </svg>
        <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
          Mostrando estadísticas acumuladas por coordinador del
          {{ fechaInicioControl.value }} al
          {{ fechaFinControl.value }}
        </span>
      </div>
    </div>
  </div>

  <!-- Cabecera -->
  <div
    class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6"
  >
    <div class="flex flex-col">
      <p class="text-gray-600 dark:text-gray-400">
        Resumen de estadísticas agrupadas por supervisor
      </p>
    </div>

    <!-- Controles de filtro -->
    <div
      class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4 w-full sm:w-auto"
    >
      <!-- Selector de Sede -->
      <div class="w-full sm:w-48">
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Sede</mat-label>
          <mat-select [formControl]="sedeControl">
            <mat-option value="todas">Todas las sedes</mat-option>
            <mat-option *ngFor="let sede of sedes" [value]="sede.id">
              {{ sede.nombre }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- Campo Fecha Inicio -->
      <div class="w-full sm:w-48">
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Fecha Inicio</mat-label>
          <input matInput type="date" [formControl]="fechaInicioControl" />
        </mat-form-field>
      </div>

      <!-- Campo Fecha Fin -->
      <div class="w-full sm:w-48">
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Fecha Fin</mat-label>
          <input matInput type="date" [formControl]="fechaFinControl" />
        </mat-form-field>
      </div>

      <!-- Botón Exportar Tabla de Estadísticas -->
      <div class="w-full sm:w-auto">
        <label class="block text-sm font-medium text-transparent mb-1"
          >&nbsp;</label
        >
        <button
          (click)="exportarTablaEstadisticas()"
          [disabled]="
            loading || exportTablaLoading || estadisticas.length === 0
          "
          class="w-full sm:w-auto px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white text-sm font-medium rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center gap-2"
          title="Exportar tabla de estadísticas de coordinador a Excel"
        >
          <svg
            *ngIf="!exportTablaLoading"
            class="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            ></path>
          </svg>
          <div
            *ngIf="exportTablaLoading"
            class="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"
          ></div>
          <span>{{
            exportTablaLoading ? "Exportando..." : "Exportar Excel"
          }}</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Indicador de carga mejorado -->
  <div
    *ngIf="loading"
    class="flex flex-col items-center justify-center p-12 bg-gray-50 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg"
  >
    <div class="relative">
      <div
        class="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 dark:border-gray-700"
      ></div>
      <div
        class="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent absolute top-0 left-0"
      ></div>
    </div>
    <div class="mt-4 text-center">
      <p class="text-gray-600 dark:text-gray-400 font-medium">
        Cargando estadísticas de coordinador...
      </p>
      <p class="text-sm text-gray-500 dark:text-gray-500 mt-1">
        Agrupando datos por coordinador
      </p>
    </div>
  </div>

  <!-- Tabla de estadísticas -->
  <div *ngIf="!loading" class="overflow-x-auto rounded-xl shadow">
    <table
      class="min-w-full border border-gray-200 dark:border-gray-700 text-sm text-left text-gray-700 dark:text-white"
    >
      <thead>
        <tr>
          <th
            class="px-6 py-3 bg-purple-500 dark:bg-purple-700 text-white uppercase text-xs font-semibold"
          >
            Sede
          </th>
          <th
            class="px-6 py-3 bg-purple-500 dark:bg-purple-700 text-white uppercase text-xs font-semibold"
          >
            Coordinador
          </th>
          <th
            class="px-6 py-3 bg-purple-500 dark:bg-purple-700 text-white uppercase text-xs font-semibold text-center"
          >
            Total Toma de Datos
          </th>
          <th
            class="px-6 py-3 bg-purple-500 dark:bg-purple-700 text-white uppercase text-xs font-semibold text-center"
          >
            Total Seguros
          </th>
          <th
            class="px-6 py-3 bg-purple-500 dark:bg-purple-700 text-white uppercase text-xs font-semibold text-center"
          >
            Total Energía
          </th>
          <th
            class="px-6 py-3 bg-purple-500 dark:bg-purple-700 text-white uppercase text-xs font-semibold text-center"
          >
            Total Lowi
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="let estadistica of estadisticas"
          class="hover:bg-gray-50 dark:hover:bg-gray-800"
        >
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 font-medium"
          >
            {{ estadistica.sede }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 font-medium text-purple-600 dark:text-purple-400"
          >
            {{ estadistica.coordinador }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 text-center font-bold text-blue-600"
          >
            {{ estadistica.totalTomaDatos }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 text-center font-bold text-green-600"
          >
            {{ estadistica.totalSeguros }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 text-center font-bold text-orange-600"
          >
            {{ estadistica.totalEnergia }}
          </td>
          <td
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 text-center font-bold text-purple-600"
          >
            {{ estadistica.totalLowi }}
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Subtotales por sede -->
    <table
      *ngIf="estadisticas.length > 0"
      class="min-w-full border-t-2 border-gray-300 dark:border-gray-600 text-sm text-left text-gray-700 dark:text-white"
    >
      <tbody>
        <tr
          *ngFor="let sede of getSedesUnicas()"
          class="bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
        >
          <td
            class="px-6 py-3 font-semibold text-gray-700 dark:text-gray-300"
            colspan="2"
          >
          TOTAL SUMATORIA DE COORDINADORES DE {{ sede }}
          </td>
          <td
            class="px-6 py-3 text-center font-semibold text-blue-600 dark:text-blue-400"
          >
            {{ getSubtotalPorSede(sede, "totalTomaDatos") }}
          </td>
          <td
            class="px-6 py-3 text-center font-semibold text-green-600 dark:text-green-400"
          >
            {{ getSubtotalPorSede(sede, "totalSeguros") }}
          </td>
          <td
            class="px-6 py-3 text-center font-semibold text-orange-600 dark:text-orange-400"
          >
            {{ getSubtotalPorSede(sede, "totalEnergia") }}
          </td>
          <td
            class="px-6 py-3 text-center font-semibold text-purple-600 dark:text-purple-400"
          >
            {{ getSubtotalPorSede(sede, "totalLowi") }}
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Fila de totales generales -->
    <table
      *ngIf="estadisticas.length > 0"
      class="min-w-full border-t-2 border-purple-500 text-sm text-left text-gray-700 dark:text-white"
    >
      <tbody>
        <tr class="bg-purple-50 dark:bg-purple-900/20">
          <td
            class="px-6 py-4 font-bold text-purple-800 dark:text-purple-200"
            colspan="2"
          >
            TOTAL SUMATORIA DE COORDINADORES
          </td>
          <td
            class="px-6 py-4 text-center font-bold text-blue-700 dark:text-blue-300"
          >
            {{ getTotalTomaDatos() }}
          </td>
          <td
            class="px-6 py-4 text-center font-bold text-green-700 dark:text-green-300"
          >
            {{ getTotalSeguros() }}
          </td>
          <td
            class="px-6 py-4 text-center font-bold text-orange-700 dark:text-orange-300"
          >
            {{ getTotalEnergia() }}
          </td>
          <td
            class="px-6 py-4 text-center font-bold text-purple-700 dark:text-purple-300"
          >
            {{ getTotalLowi() }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Paginación inferior -->
  <mat-paginator
    #coordinadorPaginator
    *ngIf="!loading && estadisticas && estadisticas.length > 0"
    class="bg-white dark:bg-gray-900 border-t-0 border border-gray-200 dark:border-gray-700"
    [length]="totalElements"
    [pageIndex]="currentPage"
    [pageSize]="pageSize"
    [pageSizeOptions]="pageSizeOptions"
    [showFirstLastButtons]="true"
    (page)="handlePageEvent($event)"
  >
  </mat-paginator>

  <!-- Estado vacío -->
  <div
    *ngIf="!loading && estadisticas?.length === 0"
    class="flex flex-col items-center justify-center p-12 text-center bg-gray-50 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg mt-6"
  >
    <div class="text-5xl text-gray-400 dark:text-gray-600 mb-4">👥</div>
    <p class="text-gray-600 dark:text-gray-400">
      No hay estadísticas de coordinador disponibles para los filtros
      seleccionados
    </p>
  </div>
</div>
