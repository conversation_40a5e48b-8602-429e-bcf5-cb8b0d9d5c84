// Estilos específicos para el componente de estadísticas de coordinador
.estadisticas-coordinador {
  &__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 0.75rem 0.75rem 0 0;
  }

  &__table {
    border-collapse: collapse;
    width: 100%;
    
    th {
      background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
      color: white;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    td {
      transition: background-color 0.2s ease;
    }

    tr:hover td {
      background-color: rgba(139, 92, 246, 0.05);
    }
  }

  &__total-row {
    background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
    font-weight: bold;
    
    td {
      border-top: 2px solid #8b5cf6;
      padding: 1rem 1.5rem;
    }
  }

  &__metric {
    &--toma-datos {
      color: #2563eb;
    }

    &--seguros {
      color: #059669;
    }

    &--energia {
      color: #d97706;
    }

    &--lowi {
      color: #7c3aed;
    }
  }

  &__loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    
    &-spinner {
      position: relative;
      width: 3rem;
      height: 3rem;
      
      &::before,
      &::after {
        content: '';
        position: absolute;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      &::before {
        width: 100%;
        height: 100%;
        border: 4px solid #e5e7eb;
      }
      
      &::after {
        width: 100%;
        height: 100%;
        border: 4px solid #8b5cf6;
        border-top-color: transparent;
      }
    }
  }

  &__empty-state {
    text-align: center;
    padding: 3rem;
    color: #6b7280;
    
    &-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .estadisticas-coordinador {
    &__table {
      font-size: 0.875rem;
      
      th,
      td {
        padding: 0.75rem 0.5rem;
      }
    }
  }
}

// Dark mode adjustments
@media (prefers-color-scheme: dark) {
  .estadisticas-coordinador {
    &__total-row {
      background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(168, 85, 247, 0.2) 100%);
    }
  }
}
