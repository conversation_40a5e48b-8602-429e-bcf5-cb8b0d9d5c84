import {
  Component,
  OnInit,
  OnDestroy,
  ViewChild,
  AfterViewInit,
  ChangeDetectorRef,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { Subject, takeUntil, debounceTime, map, Observable } from 'rxjs';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { EstadisticasSedeService } from '../../services/estadisticas-sede.service';
import { SedeService } from '@app/services/sede.service';
import Swal from 'sweetalert2';

export interface EstadisticaCoordinador {
  sede: string;
  coordinador: string;
  totalSeguros: number;
  totalEnergia: number;
  totalLowi: number;
  totalTomaDatos: number;
}

@Component({
  selector: 'app-estadisticas-coordinador',
  templateUrl: './estadisticas-coordinador.component.html',
})
export class EstadisticasCoordinadorComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  private destroy$ = new Subject<void>();

  // ViewChild para el paginador
  @ViewChild('coordinadorPaginator') paginator!: MatPaginator;

  // Controles de formulario
  sedeControl = new FormControl('todas');
  fechaInicioControl = new FormControl(new Date().toISOString().split('T')[0]);
  fechaFinControl = new FormControl('');

  // Datos
  sedes: any[] = [];
  estadisticas: EstadisticaCoordinador[] = [];
  loading = false;
  exportTablaLoading = false;

  // Paginación
  currentPage = 0;
  pageSize = 10;
  totalPages = 0;
  totalElements = 0;
  hasNext = false;
  hasPrevious = false;

  // Opciones de tamaño de página
  pageSizeOptions = [5, 10, 20, 50];

  // Configuración de tabla
  displayedColumns: string[] = [
    'sede',
    'coordinador',
    'totalTomaDatos',
    'totalSeguros',
    'totalEnergia',
    'totalLowi',
  ];

  constructor(
    private estadisticasSedeService: EstadisticasSedeService,
    private sedeService: SedeService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.cargarSedes();
    this.configurarSuscripciones();
    this.cargarEstadisticas();
  }

  ngAfterViewInit(): void {
    // Sincronizar el paginador después de que se inicialice la vista
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private cargarSedes(): void {
    this.sedeService
      .getAllSedes()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.sedes = response.data;
          }
        },
        error: (error) => {
          console.error('Error al cargar sedes:', error);
        },
      });
  }

  private configurarSuscripciones(): void {
    // Escuchar cambios en la sede
    this.sedeControl.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((sedeValue) => {
        console.log('Sede cambió a:', sedeValue);
        this.resetearPaginacion();
        this.cargarEstadisticas();
      });

    // Escuchar cambios en la fecha inicio
    this.fechaInicioControl.valueChanges
      .pipe(debounceTime(300), takeUntil(this.destroy$))
      .subscribe((fechaValue) => {
        console.log('Fecha Inicio cambió a:', fechaValue);
        this.resetearPaginacion();
        this.cargarEstadisticas();
      });

    // Escuchar cambios en la fecha fin
    this.fechaFinControl.valueChanges
      .pipe(debounceTime(300), takeUntil(this.destroy$))
      .subscribe((fechaValue) => {
        console.log('Fecha Fin cambió a:', fechaValue);
        this.resetearPaginacion();
        this.cargarEstadisticas();
      });
  }

  private cargarEstadisticas(): void {
    this.loading = true;
    const sedeIdValue = this.sedeControl.value;
    const sedeId = sedeIdValue === 'todas' ? null : Number(sedeIdValue);
    const fechaInicio = this.fechaInicioControl.value || '';
    const fechaFin = this.fechaFinControl.value || '';

    console.log('=== CARGANDO ESTADÍSTICAS DE COORDINADOR ===');
    console.log('SedeId:', sedeId);
    console.log('Fecha Inicio:', fechaInicio);
    console.log('Fecha Fin:', fechaFin);

    // Determinar si es rango de fechas o fecha específica
    const esRangoFechas: boolean = !!(fechaFin && fechaFin !== fechaInicio);

    if (esRangoFechas) {
      this.cargarEstadisticasPorRango(sedeId, fechaInicio, fechaFin);
    } else {
      this.cargarEstadisticasPorFecha(sedeId, fechaInicio);
    }
  }

  private cargarEstadisticasPorFecha(
    sedeId: number | null,
    fecha: string
  ): void {
    // Por ahora, vamos a simular los datos agrupando por coordinador
    // Más adelante implementaremos el endpoint específico en el backend
    this.estadisticasSedeService
      .obtenerEstadisticasPorSedePaginado(
        sedeId,
        null,
        fecha,
        this.currentPage,
        1000
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            const estadisticasIndividuales = response.data.estadisticas;
            this.procesarEstadisticasCoordinador(estadisticasIndividuales);
          } else {
            this.resetearDatos();
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('Error al cargar estadísticas:', error);
          this.resetearDatos();
          this.loading = false;
        },
      });
  }

  private cargarEstadisticasPorRango(
    sedeId: number | null,
    fechaInicio: string,
    fechaFin: string
  ): void {
    this.estadisticasSedeService
      .obtenerEstadisticasPorRangoFechas(
        sedeId,
        null,
        fechaInicio,
        fechaFin,
        this.currentPage,
        1000
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            const estadisticasIndividuales = response.data.estadisticas;
            this.procesarEstadisticasCoordinador(estadisticasIndividuales);
          } else {
            this.resetearDatos();
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('Error al cargar estadísticas:', error);
          this.resetearDatos();
          this.loading = false;
        },
      });
  }

  private procesarEstadisticasCoordinador(
    estadisticasIndividuales: any[]
  ): void {
    // Agrupar estadísticas por coordinador
    const agrupadas = new Map<string, EstadisticaCoordinador>();

    estadisticasIndividuales.forEach((estadistica) => {
      const key = `${estadistica.sede}-${estadistica.supervisor}`;

      if (agrupadas.has(key)) {
        const existente = agrupadas.get(key)!;
        existente.totalTomaDatos += estadistica.tomaDatos || 0;
        existente.totalSeguros += estadistica.interesadosSeguro || 0;
        existente.totalEnergia += estadistica.interesadosEnergia || 0;
        existente.totalLowi += estadistica.interesadosLowi || 0;
      } else {
        agrupadas.set(key, {
          sede: estadistica.sede,
          coordinador: estadistica.supervisor,
          totalTomaDatos: estadistica.tomaDatos || 0,
          totalSeguros: estadistica.interesadosSeguro || 0,
          totalEnergia: estadistica.interesadosEnergia || 0,
          totalLowi: estadistica.interesadosLowi || 0,
        });
      }
    });

    this.estadisticas = Array.from(agrupadas.values());
    this.totalElements = this.estadisticas.length;
    this.totalPages = Math.ceil(this.totalElements / this.pageSize);
    this.hasNext = this.currentPage < this.totalPages - 1;
    this.hasPrevious = this.currentPage > 0;

    console.log('Estadísticas agrupadas por coordinador:', this.estadisticas);
  }

  private resetearPaginacion(): void {
    this.currentPage = 0;
    if (this.paginator) {
      setTimeout(() => {
        if (this.paginator) {
          this.paginator.pageIndex = 0;
          this.paginator.firstPage();
          this.cdr.detectChanges();
        }
      }, 0);
    }
  }

  private resetearDatos(): void {
    this.estadisticas = [];
    this.currentPage = 0;
    this.totalPages = 0;
    this.totalElements = 0;
    this.hasNext = false;
    this.hasPrevious = false;

    if (this.paginator) {
      this.paginator.pageIndex = 0;
      this.paginator.length = 0;
    }
  }

  onPageChange(page: number): void {
    if (page >= 0 && page < this.totalPages) {
      this.currentPage = page;
      this.cargarEstadisticas();
    }
  }

  onPageSizeChange(newSize: number): void {
    this.pageSize = newSize;
    this.resetearPaginacion();
    this.cargarEstadisticas();
  }

  handlePageEvent(event: PageEvent): void {
    console.log('=== HANDLE PAGE EVENT ===');
    console.log(
      'event.pageIndex:',
      event.pageIndex,
      'event.pageSize:',
      event.pageSize
    );

    let shouldReload = false;

    if (event.pageSize !== this.pageSize) {
      this.pageSize = event.pageSize;
      this.currentPage = 0;
      shouldReload = true;
    } else if (event.pageIndex !== this.currentPage) {
      this.currentPage = event.pageIndex;
      shouldReload = true;
    }

    if (shouldReload) {
      this.cargarEstadisticas();
    }
  }

  /**
   * Exporta la tabla de estadísticas de coordinador a Excel
   */
  exportarTablaEstadisticas(): void {
    this.exportTablaLoading = true;

    const sedeIdValue = this.sedeControl.value;
    const sedeId = sedeIdValue === 'todas' ? null : Number(sedeIdValue);
    const fechaInicio = this.fechaInicioControl.value || '';
    const fechaFin = this.fechaFinControl.value || '';

    // Determinar si es rango de fechas o fecha única
    const esRangoFechas: boolean = !!(fechaFin && fechaFin !== fechaInicio);

    console.log('=== EXPORTANDO TABLA DE ESTADÍSTICAS DE COORDINADOR ===');
    console.log('Es rango de fechas:', esRangoFechas);

    // Obtener todos los datos para exportar
    this.obtenerTodosLosDatos(sedeId, fechaInicio, fechaFin, esRangoFechas)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (todasLasEstadisticas: EstadisticaCoordinador[]) => {
          this.generarExcelEstadisticas(
            todasLasEstadisticas,
            sedeId,
            fechaInicio,
            fechaFin,
            esRangoFechas
          );
          this.exportTablaLoading = false;
        },
        error: (error: any) => {
          console.error('Error al obtener datos para exportar:', error);
          this.exportTablaLoading = false;

          Swal.fire({
            title: 'Error al exportar',
            text: 'No se pudieron obtener los datos para la exportación. Por favor, inténtelo de nuevo.',
            icon: 'error',
            confirmButtonText: 'Entendido',
          });
        },
      });
  }

  private obtenerTodosLosDatos(
    sedeId: number | null,
    fechaInicio: string,
    fechaFin: string,
    esRangoFechas: boolean
  ): Observable<EstadisticaCoordinador[]> {
    const pageSize = 1000;
    const page = 0;

    if (esRangoFechas) {
      return this.estadisticasSedeService
        .obtenerEstadisticasPorRangoFechas(
          sedeId,
          null,
          fechaInicio,
          fechaFin,
          page,
          pageSize
        )
        .pipe(
          map((response) => {
            if (response.rpta === 1 && response.data) {
              const estadisticasIndividuales = response.data.estadisticas;
              return this.agruparPorCoordinador(estadisticasIndividuales);
            }
            return [];
          })
        );
    } else {
      return this.estadisticasSedeService
        .obtenerEstadisticasPorSedePaginado(
          sedeId,
          null,
          fechaInicio,
          page,
          pageSize
        )
        .pipe(
          map((response) => {
            if (response.rpta === 1 && response.data) {
              const estadisticasIndividuales = response.data.estadisticas;
              return this.agruparPorCoordinador(estadisticasIndividuales);
            }
            return [];
          })
        );
    }
  }

  private agruparPorCoordinador(
    estadisticasIndividuales: any[]
  ): EstadisticaCoordinador[] {
    const agrupadas = new Map<string, EstadisticaCoordinador>();

    estadisticasIndividuales.forEach((estadistica) => {
      const key = `${estadistica.sede}-${estadistica.supervisor}`;

      if (agrupadas.has(key)) {
        const existente = agrupadas.get(key)!;
        existente.totalTomaDatos += estadistica.tomaDatos || 0;
        existente.totalSeguros += estadistica.interesadosSeguro || 0;
        existente.totalEnergia += estadistica.interesadosEnergia || 0;
        existente.totalLowi += estadistica.interesadosLowi || 0;
      } else {
        agrupadas.set(key, {
          sede: estadistica.sede,
          coordinador: estadistica.supervisor,
          totalTomaDatos: estadistica.tomaDatos || 0,
          totalSeguros: estadistica.interesadosSeguro || 0,
          totalEnergia: estadistica.interesadosEnergia || 0,
          totalLowi: estadistica.interesadosLowi || 0,
        });
      }
    });

    return Array.from(agrupadas.values());
  }

  private generarExcelEstadisticas(
    estadisticas: EstadisticaCoordinador[],
    sedeId: number | null,
    fechaInicio: string,
    fechaFin: string,
    esRangoFechas: boolean
  ): void {
    // Importar dinámicamente la librería xlsx
    import('xlsx')
      .then((XLSX) => {
        // Ordenar estadísticas por sede para agruparlas
        const estadisticasOrdenadas = [...estadisticas].sort((a, b) =>
          a.sede.localeCompare(b.sede)
        );

        // Agrupar estadísticas por sede para crear subtotales
        const estadisticasPorSede = new Map<string, EstadisticaCoordinador[]>();
        estadisticasOrdenadas.forEach((estadistica) => {
          if (!estadisticasPorSede.has(estadistica.sede)) {
            estadisticasPorSede.set(estadistica.sede, []);
          }
          estadisticasPorSede.get(estadistica.sede)!.push(estadistica);
        });

        // Preparar los datos para Excel con subtotales por sede
        const datosExcel: any[] = [];
        let numeroFila = 1;

        // Obtener sedes ordenadas
        const sedesOrdenadas = Array.from(estadisticasPorSede.keys()).sort();

        // Agregar datos por sede con subtotales
        sedesOrdenadas.forEach((nombreSede) => {
          const coordinadoresDeSede = estadisticasPorSede.get(nombreSede)!;

          // Agregar coordinadores de la sede
          coordinadoresDeSede.forEach((estadistica) => {
            datosExcel.push({
              'N°': numeroFila++,
              Sede: estadistica.sede,
              Coordinador: estadistica.coordinador,
              'Total Toma de Datos': estadistica.totalTomaDatos,
              'Total Seguros': estadistica.totalSeguros,
              'Total Energía': estadistica.totalEnergia,
              'Total Lowi': estadistica.totalLowi,
            });
          });

          // Calcular subtotales de la sede
          const subtotalTomaDatos = coordinadoresDeSede.reduce(
            (sum, est) => sum + est.totalTomaDatos,
            0
          );
          const subtotalSeguros = coordinadoresDeSede.reduce(
            (sum, est) => sum + est.totalSeguros,
            0
          );
          const subtotalEnergia = coordinadoresDeSede.reduce(
            (sum, est) => sum + est.totalEnergia,
            0
          );
          const subtotalLowi = coordinadoresDeSede.reduce(
            (sum, est) => sum + est.totalLowi,
            0
          );

          // Agregar fila de subtotal de la sede
          datosExcel.push({
            'N°': '',
            Sede: `SUBTOTAL ${nombreSede}`,
            Coordinador: '',
            'Total Toma de Datos': subtotalTomaDatos,
            'Total Seguros': subtotalSeguros,
            'Total Energía': subtotalEnergia,
            'Total Lowi': subtotalLowi,
          });
        });

        // Calcular totales generales
        const totalTomaDatos = estadisticas.reduce(
          (sum, est) => sum + est.totalTomaDatos,
          0
        );
        const totalSeguros = estadisticas.reduce(
          (sum, est) => sum + est.totalSeguros,
          0
        );
        const totalEnergia = estadisticas.reduce(
          (sum, est) => sum + est.totalEnergia,
          0
        );
        const totalLowi = estadisticas.reduce(
          (sum, est) => sum + est.totalLowi,
          0
        );

        // Agregar fila de totales generales
        datosExcel.push({
          'N°': null as any,
          Sede: 'TOTAL SUMATORIA DE COORDINADORES',
          Coordinador: '',
          'Total Toma de Datos': totalTomaDatos,
          'Total Seguros': totalSeguros,
          'Total Energía': totalEnergia,
          'Total Lowi': totalLowi,
        });

        // Crear el libro de trabajo
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.json_to_sheet(datosExcel);

        // Configurar el ancho de las columnas
        const columnWidths = [
          { wch: 5 }, // N°
          { wch: 20 }, // Sede
          { wch: 30 }, // Coordinador
          { wch: 20 }, // Total Toma de Datos
          { wch: 15 }, // Total Seguros
          { wch: 15 }, // Total Energía
          { wch: 15 }, // Total Lowi
        ];
        worksheet['!cols'] = columnWidths;

        // Agregar la hoja al libro
        XLSX.utils.book_append_sheet(
          workbook,
          worksheet,
          'Estadísticas Coordinador'
        );

        // Crear nombre del archivo
        let nombreArchivo = 'estadisticas_coordinador_';
        if (esRangoFechas) {
          nombreArchivo += `${fechaInicio}_a_${fechaFin}`;
        } else {
          nombreArchivo += fechaInicio;
        }

        if (sedeId !== null) {
          const sedeNombre =
            this.sedes.find((s) => s.id === sedeId)?.nombre || sedeId;
          nombreArchivo += `_sede_${sedeNombre}`;
        }

        nombreArchivo += '.xlsx';

        // Descargar el archivo
        XLSX.writeFile(workbook, nombreArchivo);

        // Mostrar mensaje de éxito
        const numSedes = estadisticasPorSede.size;
        Swal.fire({
          title: 'Exportación exitosa',
          text: `Se han exportado ${estadisticas.length} registros de estadísticas de coordinador a Excel (incluye ${numSedes} subtotales por sede y total general)`,
          icon: 'success',
          timer: 3000,
          showConfirmButton: false,
        });
      })
      .catch((error: any) => {
        console.error('Error al cargar la librería xlsx:', error);
        Swal.fire({
          title: 'Error al exportar',
          text: 'No se pudo cargar la librería de exportación. Por favor, inténtelo de nuevo.',
          icon: 'error',
          confirmButtonText: 'Entendido',
        });
      });
  }

  // Métodos para calcular totales
  getTotalTomaDatos(): number {
    return this.estadisticas.reduce(
      (total, estadistica) => total + estadistica.totalTomaDatos,
      0
    );
  }

  getTotalSeguros(): number {
    return this.estadisticas.reduce(
      (total, estadistica) => total + estadistica.totalSeguros,
      0
    );
  }

  getTotalEnergia(): number {
    return this.estadisticas.reduce(
      (total, estadistica) => total + estadistica.totalEnergia,
      0
    );
  }

  getTotalLowi(): number {
    return this.estadisticas.reduce(
      (total, estadistica) => total + estadistica.totalLowi,
      0
    );
  }

  // Métodos para obtener subtotales por sede
  getSedesUnicas(): string[] {
    const sedes = [...new Set(this.estadisticas.map((est) => est.sede))];
    return sedes.sort();
  }

  getSubtotalPorSede(
    sede: string,
    metrica: 'totalTomaDatos' | 'totalSeguros' | 'totalEnergia' | 'totalLowi'
  ): number {
    return this.estadisticas
      .filter((est) => est.sede === sede)
      .reduce((total, estadistica) => total + estadistica[metrica], 0);
  }
}
