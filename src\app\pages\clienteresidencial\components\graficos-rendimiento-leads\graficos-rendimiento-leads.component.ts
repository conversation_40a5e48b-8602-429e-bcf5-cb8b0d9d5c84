import {
  Component,
  OnInit,
  On<PERSON><PERSON>roy,
  ViewChild,
  ElementRef,
  AfterViewInit,
  ChangeDetectorRef,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { Subject, takeUntil, debounceTime } from 'rxjs';
import { Chart, registerables } from 'chart.js';
import { EstadisticasSedeService } from '../../services/estadisticas-sede.service';
import { SedeService } from '@app/services/sede.service';

export interface RendimientoAsesor {
  nombreAsesor: string;
  sede: string;
  supervisor: string;
  totalLeads: number;
  periodo: string;
}

@Component({
  selector: 'app-graficos-rendimiento-leads',
  templateUrl: './graficos-rendimiento-leads.component.html',
})
export class GraficosRendimientoLeadsComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @ViewChild('chartRendimiento') chartRendimientoRef!: ElementRef;

  private destroy$ = new Subject<void>();

  // Controles de formulario
  periodoControl = new FormControl('diario');
  sedeControl = new FormControl('todas');
  supervisorControl = new FormControl('todos');
  fechaControl = new FormControl(new Date().toISOString().split('T')[0]);

  // Datos
  sedes: any[] = [];
  supervisores: any[] = [];
  rendimientoData: RendimientoAsesor[] = [];
  loading = false;

  // Instancia del gráfico
  chartRendimiento: Chart<any, any, any> | null = null;

  // Opciones de período
  periodos = [
    { value: 'diario', label: 'Diario' },
    { value: 'semanal', label: 'Semanal' },
    { value: 'mensual', label: 'Mensual' },
  ];

  // Contadores totales
  totalLeadsHoy = 0;
  totalAsesoresActivos = 0;
  promedioLeadsPorAsesor = 0;

  constructor(
    private estadisticasSedeService: EstadisticasSedeService,
    private sedeService: SedeService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    // Registrar Chart.js
    Chart.register(...registerables);

    this.cargarSedes();
    this.configurarSuscripciones();
    this.cargarDatosRendimiento();
  }

  ngAfterViewInit(): void {
    // Asegurar que el canvas esté disponible
    setTimeout(() => {
      if (this.chartRendimientoRef) {
        this.renderizarGrafico();
      }
    }, 100);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    if (this.chartRendimiento) {
      this.chartRendimiento.destroy();
    }
  }

  private cargarSedes(): void {
    this.sedeService
      .getAllSedes()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.sedes = response.data;
          }
        },
        error: (error) => {
          console.error('Error al cargar sedes:', error);
        },
      });
  }

  private configurarSuscripciones(): void {
    // Suscripción a cambios en sede para cargar supervisores
    this.sedeControl.valueChanges
      .pipe(takeUntil(this.destroy$), debounceTime(300))
      .subscribe((sedeId) => {
        if (sedeId && sedeId !== 'todas') {
          this.cargarSupervisores(Number(sedeId));
        } else {
          this.supervisores = [];
          this.supervisorControl.setValue('todos');
        }
        this.cargarDatosRendimiento();
      });

    // Suscripción a cambios en supervisor
    this.supervisorControl.valueChanges
      .pipe(takeUntil(this.destroy$), debounceTime(300))
      .subscribe(() => {
        this.cargarDatosRendimiento();
      });

    // Suscripción a cambios en período
    this.periodoControl.valueChanges
      .pipe(takeUntil(this.destroy$), debounceTime(300))
      .subscribe(() => {
        this.cargarDatosRendimiento();
      });

    // Suscripción a cambios en fecha
    this.fechaControl.valueChanges
      .pipe(takeUntil(this.destroy$), debounceTime(300))
      .subscribe(() => {
        this.cargarDatosRendimiento();
      });
  }

  private cargarSupervisores(sedeId: number): void {
    this.estadisticasSedeService
      .obtenerSupervisoresPorSede(sedeId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.rpta === 1 && response.data) {
            this.supervisores = response.data;
          }
        },
        error: (error) => {
          console.error('Error al cargar supervisores:', error);
        },
      });
  }

  private cargarDatosRendimiento(): void {
    this.loading = true;

    const periodo = this.periodoControl.value as
      | 'diario'
      | 'semanal'
      | 'mensual';
    const sedeId =
      this.sedeControl.value === 'todas'
        ? null
        : Number(this.sedeControl.value);
    const supervisorId =
      this.supervisorControl.value === 'todos'
        ? null
        : Number(this.supervisorControl.value);
    const fecha =
      this.fechaControl.value || new Date().toISOString().split('T')[0];

    this.estadisticasSedeService
      .obtenerRendimientoLeadsPorAsesor(periodo, sedeId, supervisorId, fecha)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1 && response.data) {
            this.rendimientoData = response.data;
            this.calcularContadores();
            this.renderizarGrafico();
          }
        },
        error: (error) => {
          this.loading = false;
          console.error('Error al cargar datos de rendimiento:', error);
          // Usar datos simulados para desarrollo
          this.generarDatosSimulados();
        },
      });
  }

  private generarDatosSimulados(): void {
    // Datos simulados para desarrollo
    this.rendimientoData = [
      {
        nombreAsesor: 'Juan Pérez',
        sede: 'Madrid Centro',
        supervisor: 'Ana García',
        totalLeads: 45,
        periodo: 'diario',
      },
      {
        nombreAsesor: 'María López',
        sede: 'Madrid Centro',
        supervisor: 'Ana García',
        totalLeads: 38,
        periodo: 'diario',
      },
      {
        nombreAsesor: 'Carlos Ruiz',
        sede: 'Barcelona Norte',
        supervisor: 'Luis Martín',
        totalLeads: 42,
        periodo: 'diario',
      },
      {
        nombreAsesor: 'Laura Sánchez',
        sede: 'Valencia Sur',
        supervisor: 'Carmen Díaz',
        totalLeads: 35,
        periodo: 'diario',
      },
      {
        nombreAsesor: 'Pedro Gómez',
        sede: 'Madrid Centro',
        supervisor: 'Ana García',
        totalLeads: 40,
        periodo: 'diario',
      },
      {
        nombreAsesor: 'Ana Fernández',
        sede: 'Barcelona Norte',
        supervisor: 'Luis Martín',
        totalLeads: 33,
        periodo: 'diario',
      },
      {
        nombreAsesor: 'Miguel Torres',
        sede: 'Valencia Sur',
        supervisor: 'Carmen Díaz',
        totalLeads: 37,
        periodo: 'diario',
      },
      {
        nombreAsesor: 'Elena Morales',
        sede: 'Madrid Centro',
        supervisor: 'Ana García',
        totalLeads: 41,
        periodo: 'diario',
      },
    ];

    this.calcularContadores();
    this.renderizarGrafico();
  }

  private calcularContadores(): void {
    this.totalLeadsHoy = this.rendimientoData.reduce(
      (sum, item) => sum + item.totalLeads,
      0
    );
    this.totalAsesoresActivos = this.rendimientoData.length;
    this.promedioLeadsPorAsesor =
      this.totalAsesoresActivos > 0
        ? Math.round(this.totalLeadsHoy / this.totalAsesoresActivos)
        : 0;
  }

  private renderizarGrafico(): void {
    if (!this.chartRendimientoRef || !this.chartRendimientoRef.nativeElement) {
      return;
    }

    // Destruir gráfico existente
    if (this.chartRendimiento) {
      this.chartRendimiento.destroy();
    }

    // Ordenar datos por total de leads (descendente) y tomar los top 10
    const topAsesores = this.rendimientoData
      .sort((a, b) => b.totalLeads - a.totalLeads)
      .slice(0, 10);

    if (topAsesores.length === 0) {
      return;
    }

    const labels = topAsesores.map((item) => item.nombreAsesor);
    const data = topAsesores.map((item) => item.totalLeads);

    // Generar colores dinámicos
    const backgroundColors = labels.map((_, index) => {
      const hue = (index * 137.5) % 360; // Distribución dorada para colores diversos
      return `hsla(${hue}, 70%, 60%, 0.8)`;
    });

    const ctx = this.chartRendimientoRef.nativeElement.getContext('2d');

    this.chartRendimiento = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [
          {
            label: 'Leads Generados',
            data: data,
            backgroundColor: backgroundColors,
            borderColor: backgroundColors.map((color) =>
              color.replace('0.8', '1')
            ),
            borderWidth: 2,
            borderRadius: 6,
            maxBarThickness: 40,
          },
        ],
      },
      options: {
        indexAxis: 'y',
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            callbacks: {
              label: (context) => {
                const asesor = topAsesores[context.dataIndex];
                return [
                  `Leads: ${context.raw}`,
                  `Sede: ${asesor.sede}`,
                  `Supervisor: ${asesor.supervisor}`,
                ];
              },
            },
          },
        },
        scales: {
          x: {
            beginAtZero: true,
            grid: {
              display: true,
              color: 'rgba(200, 200, 200, 0.3)',
            },
            ticks: {
              precision: 0,
            },
          },
          y: {
            grid: {
              display: false,
            },
          },
        },
      },
    });
  }

  // Método público para refrescar datos
  refrescarDatos(): void {
    this.cargarDatosRendimiento();
  }

  // Métodos para manejar cambios en los controles
  onPeriodoChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.periodoControl.setValue(target.value);
  }

  onSedeChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.sedeControl.setValue(target.value);
  }

  onSupervisorChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.supervisorControl.setValue(target.value);
  }

  onFechaChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.fechaControl.setValue(target.value);
  }

  // Método para obtener la etiqueta del período
  getPeriodoLabel(): string {
    const periodo = this.periodoControl.value;
    const periodoObj = this.periodos.find((p) => p.value === periodo);
    return periodoObj ? periodoObj.label : 'Diario';
  }
}
