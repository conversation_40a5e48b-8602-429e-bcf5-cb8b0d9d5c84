<div
  class="bg-gradient-to-r from-indigo-600 to-blue-500 -mx-6 -mt-6 px-6 py-4 mb-6 rounded-t-lg shadow-md"
>
  <h2 class="text-xl font-bold text-white flex items-center">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-6 w-6 mr-2"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
      />
    </svg>
    Enviar nueva notificación
  </h2>
  <p class="text-indigo-100 text-sm mt-1">
    Crea y envía notificaciones a usuarios específicos o a todos los usuarios
  </p>
</div>

<form [formGroup]="form" (ngSubmit)="enviarNotificacion()" class="space-y-6">
  <div class="space-y-6">
    <!-- Título de la notificación -->
    <div>
      <label
        for="titulo"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
      >
        Título <span class="text-red-500 dark:text-red-400">*</span>
      </label>
      <input
        id="titulo"
        type="text"
        formControlName="titulo"
        placeholder="Título de la notificación"
        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-indigo-500 dark:focus:border-indigo-400"
      />
      <div
        *ngIf="form.get('titulo')?.touched && form.get('titulo')?.invalid"
        class="mt-1 text-sm text-red-600 dark:text-red-400"
      >
        <div *ngIf="form.get('titulo')?.hasError('required')">
          El título es obligatorio
        </div>
        <div *ngIf="form.get('titulo')?.hasError('maxlength')">
          El título no puede tener más de 100 caracteres
        </div>
      </div>
    </div>

    <!-- Mensaje de la notificación -->
    <div>
      <label
        for="mensaje"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
      >
        Mensaje <span class="text-red-500 dark:text-red-400">*</span>
      </label>
      <textarea
        id="mensaje"
        formControlName="mensaje"
        placeholder="Contenido de la notificación"
        rows="4"
        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-indigo-500 dark:focus:border-indigo-400"
      ></textarea>
      <div
        *ngIf="form.get('mensaje')?.touched && form.get('mensaje')?.invalid"
        class="mt-1 text-sm text-red-600 dark:text-red-400"
      >
        <div *ngIf="form.get('mensaje')?.hasError('required')">
          El mensaje es obligatorio
        </div>
        <div *ngIf="form.get('mensaje')?.hasError('maxlength')">
          El mensaje no puede tener más de 500 caracteres
        </div>
      </div>
    </div>

    <!-- Tipo de notificación -->
    <div>
      <label
        for="tipo"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
      >
        Tipo de notificación
        <span class="text-red-500 dark:text-red-400">*</span>
      </label>
      <select
        id="tipo"
        formControlName="tipo"
        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-indigo-500 dark:focus:border-indigo-400"
      >
        <option *ngFor="let tipo of tiposNotificacion" [value]="tipo">
          {{ tipo }}
        </option>
      </select>
    </div>

    <!-- Categoría de notificación -->
    <div>
      <label
        for="categoria"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
      >
        Categoría
        <span class="text-red-500 dark:text-red-400">*</span>
      </label>
      <select
        id="categoria"
        formControlName="categoria"
        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-indigo-500 dark:focus:border-indigo-400"
      >
        <option
          *ngFor="let categoria of categoriasNotificacion"
          [value]="categoria"
        >
          {{ categoria }}
        </option>
      </select>
      <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
        Define cómo se organizará la notificación en la interfaz del usuario
      </p>
    </div>

    <!-- Destinatario -->
    <div>
      <label
        for="destinatario"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
      >
        Destinatario <span class="text-red-500 dark:text-red-400">*</span>
      </label>
      <select
        id="destinatario"
        formControlName="destinatario"
        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-indigo-500 dark:focus:border-indigo-400"
      >
        <option value="todos">Todos los usuarios</option>
        <option value="usuario">Usuario específico</option>
        <option value="rol">Por rol específico</option>
        <option value="sede">Por sede específica</option>
        <option value="sede-rol">Por sede y rol específicos</option>
      </select>
    </div>

    <!-- Selector de Rol (solo si se selecciona rol o sede-rol) -->
    <div
      *ngIf="
        form.get('destinatario')?.value === 'rol' ||
        form.get('destinatario')?.value === 'sede-rol'
      "
    >
      <label
        for="rol"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
      >
        Rol <span class="text-red-500 dark:text-red-400">*</span>
      </label>
      <select
        id="rol"
        formControlName="rol"
        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-indigo-500 dark:focus:border-indigo-400"
      >
        <option value="">Selecciona un rol</option>
        <option *ngFor="let rol of roles" [value]="rol">{{ rol }}</option>
      </select>
    </div>

    <!-- Selector de Sede (solo si se selecciona sede o sede-rol) -->
    <div
      *ngIf="
        form.get('destinatario')?.value === 'sede' ||
        form.get('destinatario')?.value === 'sede-rol'
      "
    >
      <label
        for="sede"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
      >
        Sede <span class="text-red-500 dark:text-red-400">*</span>
      </label>
      <select
        id="sede"
        formControlName="sede"
        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-indigo-500 dark:focus:border-indigo-400"
      >
        <option value="">Selecciona una sede</option>
        <option *ngFor="let sede of sedes" [value]="sede.id">
          {{ sede.nombre }}
        </option>
      </select>
    </div>

    <!-- Usuario específico (solo si se selecciona usuario específico) -->
    <div *ngIf="form.get('destinatario')?.value === 'usuario'">
      <label
        for="nombreUsuario"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
      >
        Usuario <span class="text-red-500 dark:text-red-400">*</span>
      </label>
      <div class="relative">
        <input
          type="text"
          id="nombreUsuario"
          formControlName="nombreUsuario"
          readonly
          (click)="toggleListaUsuarios()"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-indigo-500 dark:focus:border-indigo-400 cursor-pointer"
          placeholder="Selecciona un usuario"
        />
        <input type="hidden" formControlName="usuarioId" />
        <button
          type="button"
          (click)="toggleListaUsuarios()"
          class="absolute inset-y-0 right-0 flex items-center px-2 text-gray-500 dark:text-gray-400"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </button>
      </div>

      <!-- Lista de usuarios (desplegable) -->
      <div *ngIf="mostrarListaUsuarios" class="mt-1 relative">
        <div
          class="absolute z-10 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto"
        >
          <!-- Buscador -->
          <div
            class="sticky top-0 bg-white dark:bg-gray-800 p-3 border-b border-gray-200 dark:border-gray-700"
          >
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
              >
                <svg
                  class="h-5 w-5 text-gray-400 dark:text-gray-500"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Buscar por nombre, email, DNI o ID..."
                (input)="buscarUsuarios($event)"
                class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-indigo-500 dark:focus:border-indigo-400"
                autocomplete="off"
              />
            </div>
          </div>

          <!-- Cargando -->
          <div
            *ngIf="cargandoUsuarios"
            class="p-4 text-center text-gray-500 dark:text-gray-400"
          >
            <svg
              class="animate-spin h-5 w-5 mx-auto"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </div>

          <!-- Lista de usuarios -->
          <ul
            *ngIf="!cargandoUsuarios && usuariosFiltrados.length > 0"
            class="py-1 divide-y divide-gray-100 dark:divide-gray-700"
          >
            <li
              *ngFor="let usuario of usuariosFiltrados"
              (click)="seleccionarUsuario(usuario)"
              class="px-4 py-3 hover:bg-indigo-50 dark:hover:bg-indigo-900/30 cursor-pointer flex items-center transition-colors duration-150"
            >
              <div
                class="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center text-indigo-800 dark:text-indigo-300 font-semibold mr-3 flex-shrink-0"
              >
                {{
                  usuario.nombre && usuario.apellido
                    ? (usuario.nombre[0] + usuario.apellido[0]).toUpperCase()
                    : usuario.nombre
                    ? usuario.nombre[0].toUpperCase()
                    : usuario.username
                    ? usuario.username[0].toUpperCase()
                    : "U"
                }}
              </div>
              <div class="flex-1 min-w-0">
                <div
                  class="font-medium text-gray-900 dark:text-gray-100 truncate"
                >
                  {{
                    usuario.nombre && usuario.apellido
                      ? usuario.nombre + " " + usuario.apellido
                      : usuario.nombre || usuario.username || "Usuario"
                  }}
                  <span
                    *ngIf="usuario.id"
                    class="text-xs text-gray-500 dark:text-gray-400 ml-1"
                    >(ID: {{ usuario.id }})</span
                  >
                </div>
                <div
                  class="text-sm text-gray-500 dark:text-gray-400 truncate flex items-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-3 w-3 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  {{ usuario.email || "Sin email" }}
                </div>
                <div
                  *ngIf="usuario.dni"
                  class="text-xs text-gray-500 dark:text-gray-400 truncate flex items-center mt-1"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-3 w-3 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2"
                    />
                  </svg>
                  DNI: {{ usuario.dni }}
                </div>
              </div>
            </li>
          </ul>

          <!-- Mensaje sin resultados -->
          <div
            *ngIf="!cargandoUsuarios && usuariosFiltrados.length === 0"
            class="p-6 text-center"
          >
            <svg
              class="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-3"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"
              />
            </svg>
            <p class="text-gray-500 dark:text-gray-400 mb-2">
              No se encontraron usuarios
            </p>
            <p class="text-sm text-gray-400 dark:text-gray-500">
              Intenta con otro término de búsqueda
            </p>
          </div>

          <!-- Paginación -->
          <div
            *ngIf="!cargandoUsuarios && totalPaginas > 1"
            class="px-4 py-3 bg-gray-50 dark:bg-blue-900/10 border-t border-gray-200 dark:border-blue-500/20 flex justify-between items-center"
          >
            <button
              type="button"
              [disabled]="paginaActual === 1"
              (click)="cambiarPagina(paginaActual - 1)"
              class="px-3 py-1 text-sm rounded-md border border-transparent"
              [ngClass]="
                paginaActual === 1
                  ? 'text-gray-400 dark:text-gray-500 cursor-not-allowed'
                  : 'text-indigo-600 dark:text-blue-300 hover:bg-indigo-50 dark:hover:bg-blue-900/20 hover:border-indigo-200 dark:hover:border-blue-500/20'
              "
            >
              Anterior
            </button>
            <div class="flex items-center space-x-1">
              <button
                *ngFor="let pagina of obtenerPaginasVisibles()"
                (click)="cambiarPagina(pagina)"
                class="w-8 h-8 flex items-center justify-center text-sm rounded-md"
                [ngClass]="
                  pagina === paginaActual
                    ? 'bg-indigo-100 dark:bg-blue-900/30 text-indigo-700 dark:text-blue-300 font-medium'
                    : pagina === -1
                    ? 'text-gray-500 dark:text-gray-400 cursor-default'
                    : 'text-gray-700 dark:text-blue-200 hover:bg-indigo-50 dark:hover:bg-blue-900/20'
                "
              >
                {{ pagina === -1 ? "..." : pagina }}
              </button>
            </div>
            <button
              type="button"
              [disabled]="paginaActual === totalPaginas"
              (click)="cambiarPagina(paginaActual + 1)"
              class="px-3 py-1 text-sm rounded-md border border-transparent"
              [ngClass]="
                paginaActual === totalPaginas
                  ? 'text-gray-400 dark:text-gray-500 cursor-not-allowed'
                  : 'text-indigo-600 dark:text-blue-300 hover:bg-indigo-50 dark:hover:bg-blue-900/20 hover:border-indigo-200 dark:hover:border-blue-500/20'
              "
            >
              Siguiente
            </button>
          </div>
        </div>
      </div>

      <div
        *ngIf="
          form.get('nombreUsuario')?.touched &&
          form.get('nombreUsuario')?.invalid
        "
        class="mt-1 text-sm text-red-600 dark:text-red-400"
      >
        Debes seleccionar un usuario
      </div>
    </div>

    <!-- Enlace (opcional) -->
    <div>
      <label
        for="enlace"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
      >
        Enlace (opcional)
      </label>
      <input
        id="enlace"
        type="text"
        formControlName="enlace"
        placeholder="Ej: /home, /ventas, etc."
        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-indigo-500 dark:focus:border-indigo-400"
      />
      <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
        Ruta a la que se redirigirá al hacer clic en la notificación
      </p>
    </div>

    <!-- Opción para enviar por email -->
    <div class="flex items-center">
      <input
        id="enviarEmail"
        type="checkbox"
        formControlName="enviarEmail"
        class="h-4 w-4 text-indigo-600 dark:text-indigo-500 focus:ring-indigo-500 dark:focus:ring-indigo-400 border-gray-300 dark:border-gray-600 rounded"
      />
      <label
        for="enviarEmail"
        class="ml-2 block text-sm text-gray-700 dark:text-gray-300"
      >
        Enviar también por email
      </label>
    </div>
  </div>

  <div
    class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"
  >
    <button
      type="button"
      [disabled]="enviando"
      (click)="cancelar()"
      class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 disabled:opacity-50 transition-colors duration-200"
    >
      <div class="flex items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 mr-1"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
        Cancelar
      </div>
    </button>
    <button
      type="submit"
      [disabled]="form.invalid || enviando"
      class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-blue-500 hover:from-indigo-700 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 disabled:opacity-50 flex items-center transition-all duration-200"
    >
      <div *ngIf="enviando" class="flex items-center">
        <svg
          class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
        Enviando...
      </div>
      <div *ngIf="!enviando" class="flex items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 mr-1"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
          />
        </svg>
        Enviar notificación
      </div>
    </button>
  </div>
</form>
