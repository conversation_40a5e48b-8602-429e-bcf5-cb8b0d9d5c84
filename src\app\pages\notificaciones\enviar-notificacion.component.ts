import { Component, OnInit, OnDestroy, Inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TipoNotificacion } from '@app/models/notificacion.model';
import { NotificacionesWsService } from '@app/services/notificaciones/notificaciones-ws.service';
import { WebSocketService } from '@app/services/websocket/WebSocketService';
import { NotificationService } from '@app/services/notification/notification.service';
import { HttpClient } from '@angular/common/http';
import { environment } from '@src/environments/environment';
import { Subject, Subscription } from 'rxjs';
import Swal from 'sweetalert2';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { GeneralService } from '@app/services/general.service';
import { NotificationCategory } from '@app/models/backend/notification/notification-types.enum';

@Component({
  selector: 'app-enviar-notificacion',
  templateUrl: './enviar-notificacion.component.html',
})
export class EnviarNotificacionComponent implements OnInit, OnDestroy {
  form: FormGroup;
  enviando = false;
  tiposNotificacion = Object.values(TipoNotificacion);
  categoriasNotificacion = Object.values(NotificationCategory);

  // Lista de usuarios
  usuarios: any[] = [];
  usuariosFiltrados: any[] = [];
  todosLosUsuarios: any[] = []; // Lista completa de usuarios para búsqueda local
  cargandoUsuarios = false;
  mostrarListaUsuarios = false;

  // Listas para selectores
  roles: string[] = [];
  sedes: any[] = [];

  // Paginación
  paginaActual = 1;
  totalPaginas = 1;
  tamañoPagina = 10;
  totalUsuarios = 0;

  // Búsqueda
  terminoBusqueda = '';
  busquedaSubject = new Subject<string>();

  // Suscripciones
  private subscriptions: Subscription[] = [];

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<EnviarNotificacionComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private webSocketService: WebSocketService,
    private notificacionesService: NotificacionesWsService,
    private notificationService: NotificationService,
    private http: HttpClient,
    private authService: GeneralService
  ) {
    this.form = this.fb.group({
      titulo: ['', [Validators.required, Validators.maxLength(100)]],
      mensaje: ['', [Validators.required, Validators.maxLength(500)]],
      tipo: [TipoNotificacion.INFO, Validators.required],
      categoria: [NotificationCategory.GENERAL, Validators.required], // Añadimos el campo de categoría
      destinatario: ['todos'], // 'todos', 'usuario', 'rol', 'sede', 'sede-rol'
      usuarioId: [''],
      nombreUsuario: [''],
      rol: [''],
      sede: [''],
      enlace: [''],
      enviarEmail: [false],
    });
  }

  ngOnInit(): void {
    console.log('Inicializando componente de enviar notificación');

    // Inicializar el servicio de notificaciones
    if (!this.notificacionesService.isInitialized()) {
      this.notificacionesService.setupSubscriptions();
    }

    // Escuchar cambios en el tipo de destinatario
    const destinatarioControl = this.form.get('destinatario');
    if (destinatarioControl) {
      const subscription = destinatarioControl.valueChanges.subscribe(
        (valor) => {
          const usuarioIdControl = this.form.get('usuarioId');
          const nombreUsuarioControl = this.form.get('nombreUsuario');
          const rolControl = this.form.get('rol');
          const sedeControl = this.form.get('sede');

          // Limpiar todas las validaciones primero
          usuarioIdControl?.clearValidators();
          nombreUsuarioControl?.clearValidators();
          rolControl?.clearValidators();
          sedeControl?.clearValidators();

          // Limpiar valores
          usuarioIdControl?.setValue('');
          nombreUsuarioControl?.setValue('');
          rolControl?.setValue('');
          sedeControl?.setValue('');

          // Aplicar validaciones según el tipo de destinatario
          switch (valor) {
            case 'usuario':
              usuarioIdControl?.setValidators([Validators.required]);
              nombreUsuarioControl?.setValidators([Validators.required]);
              break;
            case 'rol':
              rolControl?.setValidators([Validators.required]);
              break;
            case 'sede':
              sedeControl?.setValidators([Validators.required]);
              break;
            case 'sede-rol':
              rolControl?.setValidators([Validators.required]);
              sedeControl?.setValidators([Validators.required]);
              break;
            default:
              // Para 'todos' no se requieren validaciones adicionales
              break;
          }

          // Actualizar validaciones
          usuarioIdControl?.updateValueAndValidity();
          nombreUsuarioControl?.updateValueAndValidity();
          rolControl?.updateValueAndValidity();
          sedeControl?.updateValueAndValidity();
        }
      );

      this.subscriptions.push(subscription);
    }

    // Configurar el debounce para la búsqueda
    const searchSubscription = this.busquedaSubject
      .pipe(debounceTime(200), distinctUntilChanged())
      .subscribe((termino) => {
        this.terminoBusqueda = termino;
        // Usar nuestro buscador personalizado
        this.buscarUsuariosLocal(termino);
      });

    this.subscriptions.push(searchSubscription);

    // Cargar todos los usuarios al inicio
    this.cargarTodosLosUsuarios();

    // Cargar roles y sedes
    this.cargarRoles();
    this.cargarSedes();
  }

  ngOnDestroy(): void {
    // Cancelar todas las suscripciones
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  /**
   * Carga la lista de usuarios paginada
   */
  cargarUsuarios(pagina: number = 1): void {
    this.cargandoUsuarios = true;
    this.paginaActual = pagina;

    // Construir parámetros de consulta
    const params: any = {
      page: (this.paginaActual - 1).toString(), // Ajustar para API que usa base 0
      size: this.tamañoPagina.toString(),
    };

    // Agregar término de búsqueda si existe
    if (this.terminoBusqueda) {
      params.search = this.terminoBusqueda;
    }

    // Realizar la petición HTTP
    this.http
      .get<any>(`${environment.url}api/user/listar`, { params })
      .subscribe({
        next: (respuesta) => {
          if (respuesta && respuesta.rpta === 1 && respuesta.data) {
            // Verificar si la respuesta tiene la estructura esperada para UserPageDTO
            if (respuesta.data.users && Array.isArray(respuesta.data.users)) {
              this.usuarios = respuesta.data.users || [];
              this.usuariosFiltrados = [...this.usuarios];
              this.totalUsuarios = respuesta.data.totalItems || 0;
              this.totalPaginas = respuesta.data.totalPages || 1;

              // Asegurarse de que totalPaginas sea al menos 1
              if (this.totalPaginas < 1) {
                this.totalPaginas = 1;
              }

              console.log('Usuarios cargados:', this.usuarios.length);
              console.log('Total páginas:', this.totalPaginas);
            } else {
              console.warn('Estructura de respuesta no válida:', respuesta);
              this.usuarios = [];
              this.usuariosFiltrados = [];
              this.totalUsuarios = 0;
              this.totalPaginas = 1;
            }
          } else {
            console.warn('Respuesta de API no válida:', respuesta);
            this.usuarios = [];
            this.usuariosFiltrados = [];
            this.totalUsuarios = 0;
            this.totalPaginas = 1;
          }
          this.cargandoUsuarios = false;
        },
        error: (err) => {
          console.error('Error al cargar usuarios:', err);
          this.usuarios = [];
          this.usuariosFiltrados = [];
          this.totalUsuarios = 0;
          this.totalPaginas = 1;
          this.cargandoUsuarios = false;
        },
      });
  }

  /**
   * Obtiene las páginas que se deben mostrar en el paginador
   * Muestra un máximo de 5 páginas, con elipsis si es necesario
   */
  obtenerPaginasVisibles(): number[] {
    const paginas: number[] = [];

    if (this.totalPaginas <= 7) {
      // Si hay 7 o menos páginas, mostrar todas
      for (let i = 1; i <= this.totalPaginas; i++) {
        paginas.push(i);
      }
    } else {
      // Siempre mostrar la primera página
      paginas.push(1);

      // Determinar el rango de páginas a mostrar alrededor de la página actual
      let inicio = Math.max(2, this.paginaActual - 1);
      let fin = Math.min(this.totalPaginas - 1, this.paginaActual + 1);

      // Ajustar para mostrar siempre 3 páginas en el medio
      if (inicio === 2) {
        fin = Math.min(4, this.totalPaginas - 1);
      }
      if (fin === this.totalPaginas - 1) {
        inicio = Math.max(2, this.totalPaginas - 3);
      }

      // Agregar elipsis si es necesario
      if (inicio > 2) {
        paginas.push(-1); // -1 representa elipsis
      }

      // Agregar páginas del rango
      for (let i = inicio; i <= fin; i++) {
        paginas.push(i);
      }

      // Agregar elipsis si es necesario
      if (fin < this.totalPaginas - 1) {
        paginas.push(-1); // -1 representa elipsis
      }

      // Siempre mostrar la última página
      paginas.push(this.totalPaginas);
    }

    return paginas;
  }

  /**
   * Maneja la búsqueda de usuarios desde el input
   */
  buscarUsuarios(evento: any): void {
    const termino = evento.target.value || '';
    console.log('Búsqueda iniciada con término:', termino);

    // Enviar el término al subject para procesarlo
    this.busquedaSubject.next(termino);
  }

  /**
   * Carga todos los usuarios disponibles para búsqueda local
   * Hace múltiples peticiones para obtener todos los usuarios
   */
  private cargarTodosLosUsuarios(): void {
    this.cargandoUsuarios = true;
    console.log('Cargando todos los usuarios para búsqueda local');

    // Primero cargar la primera página para saber cuántas páginas hay en total
    this.http
      .get<any>(`${environment.url}api/user/listar?page=0&size=100`)
      .subscribe({
        next: (respuesta) => {
          if (respuesta && respuesta.rpta === 1 && respuesta.data) {
            // Guardar los usuarios de la primera página
            if (respuesta.data.users && Array.isArray(respuesta.data.users)) {
              this.todosLosUsuarios = [...respuesta.data.users];

              // Configurar la paginación inicial correctamente
              this.totalUsuarios = respuesta.data.totalItems || 0;
              this.totalPaginas = Math.ceil(
                this.totalUsuarios / this.tamañoPagina
              );

              // Asegurarse de que totalPaginas sea al menos 1
              if (this.totalPaginas < 1) {
                this.totalPaginas = 1;
              }

              // Mostrar la primera página de usuarios
              this.usuarios = this.todosLosUsuarios.slice(0, this.tamañoPagina);
              this.usuariosFiltrados = [...this.usuarios];
              this.paginaActual = 1;

              console.log('Total usuarios encontrados:', this.totalUsuarios);
              console.log('Total páginas calculadas:', this.totalPaginas);
              console.log('Usuarios en primera página:', this.usuarios.length);

              // Si hay más páginas, cargarlas también
              if (respuesta.data.totalPages > 1) {
                this.cargarPaginasAdicionales(1, respuesta.data.totalPages);
              } else {
                this.cargandoUsuarios = false;
              }
            } else {
              console.warn('Estructura de respuesta no válida:', respuesta);
              this.cargandoUsuarios = false;
            }
          } else {
            console.warn('Respuesta de API no válida:', respuesta);
            this.cargandoUsuarios = false;
          }
        },
        error: (err) => {
          console.error('Error al cargar todos los usuarios:', err);
          this.cargandoUsuarios = false;
        },
      });
  }

  /**
   * Carga páginas adicionales de usuarios recursivamente
   */
  private cargarPaginasAdicionales(
    paginaActual: number,
    totalPaginas: number
  ): void {
    if (paginaActual >= totalPaginas) {
      console.log(
        'Todas las páginas cargadas. Total usuarios:',
        this.todosLosUsuarios.length
      );

      // Actualizar la paginación basada en el total de usuarios cargados
      this.totalUsuarios = this.todosLosUsuarios.length;
      this.totalPaginas = Math.ceil(this.totalUsuarios / this.tamañoPagina);

      // Asegurarse de que totalPaginas sea al menos 1
      if (this.totalPaginas < 1) {
        this.totalPaginas = 1;
      }

      // Actualizar la vista con la primera página de usuarios
      this.usuarios = this.todosLosUsuarios.slice(0, this.tamañoPagina);
      this.usuariosFiltrados = [...this.usuarios];
      this.paginaActual = 1;

      console.log('Paginación actualizada - Total páginas:', this.totalPaginas);
      console.log(
        'Usuarios mostrados en primera página:',
        this.usuarios.length
      );
      this.cargandoUsuarios = false;
      return;
    }

    this.http
      .get<any>(
        `${environment.url}api/user/listar?page=${paginaActual}&size=100`
      )
      .subscribe({
        next: (respuesta) => {
          if (
            respuesta &&
            respuesta.rpta === 1 &&
            respuesta.data &&
            respuesta.data.users &&
            Array.isArray(respuesta.data.users)
          ) {
            // Añadir usuarios a la lista completa
            this.todosLosUsuarios = [
              ...this.todosLosUsuarios,
              ...respuesta.data.users,
            ];

            console.log(
              `Página ${paginaActual} cargada. Usuarios acumulados: ${this.todosLosUsuarios.length}`
            );

            // Cargar la siguiente página
            this.cargarPaginasAdicionales(paginaActual + 1, totalPaginas);
          } else {
            console.warn(`Error al cargar página ${paginaActual}:`, respuesta);
            this.cargandoUsuarios = false;
          }
        },
        error: (err) => {
          console.error(`Error al cargar página ${paginaActual}:`, err);
          this.cargandoUsuarios = false;
        },
      });
  }

  /**
   * Busca usuarios localmente en la lista completa
   */
  private buscarUsuariosLocal(termino: string): void {
    console.log('Buscando localmente con término:', termino);

    if (!termino || termino.trim() === '') {
      // Si no hay término, mostrar la primera página de todos los usuarios
      this.usuarios = this.todosLosUsuarios.slice(0, this.tamañoPagina);
      this.usuariosFiltrados = [...this.usuarios];
      this.totalUsuarios = this.todosLosUsuarios.length;
      this.totalPaginas = Math.ceil(this.totalUsuarios / this.tamañoPagina);

      // Asegurarse de que totalPaginas sea al menos 1
      if (this.totalPaginas < 1) {
        this.totalPaginas = 1;
      }

      console.log(
        'Mostrando todos los usuarios - Total páginas:',
        this.totalPaginas
      );
      return;
    }

    const terminoLower = termino.toLowerCase().trim();

    // Filtrar la lista completa de usuarios
    const usuariosFiltrados = this.todosLosUsuarios.filter((usuario) => {
      // Verificar cada propiedad y hacer la comparación solo si existe
      const nombreMatch = usuario.nombre
        ? usuario.nombre.toLowerCase().includes(terminoLower)
        : false;

      const usernameMatch = usuario.username
        ? usuario.username.toLowerCase().includes(terminoLower)
        : false;

      const emailMatch = usuario.email
        ? usuario.email.toLowerCase().includes(terminoLower)
        : false;

      const dniMatch = usuario.dni
        ? usuario.dni.toLowerCase().includes(terminoLower)
        : false;

      // También buscar en el ID si es un número
      const idMatch =
        !isNaN(parseInt(terminoLower)) && usuario.id === parseInt(terminoLower);

      // Devolver true si cualquiera de las propiedades coincide
      return nombreMatch || usernameMatch || emailMatch || dniMatch || idMatch;
    });

    // Actualizar la lista filtrada y la paginación
    this.totalUsuarios = usuariosFiltrados.length;
    this.totalPaginas = Math.ceil(this.totalUsuarios / this.tamañoPagina);

    // Asegurarse de que totalPaginas sea al menos 1
    if (this.totalPaginas < 1) {
      this.totalPaginas = 1;
    }

    this.paginaActual = 1;

    // Mostrar la primera página de resultados
    const inicio = 0;
    const fin = Math.min(this.tamañoPagina, usuariosFiltrados.length);
    this.usuarios = usuariosFiltrados.slice(inicio, fin);
    this.usuariosFiltrados = [...this.usuarios];

    console.log(
      'Resultados encontrados:',
      this.totalUsuarios,
      'Total páginas:',
      this.totalPaginas
    );
  }

  /**
   * Cambia la página de usuarios
   */
  cambiarPagina(pagina: number): void {
    if (
      pagina >= 1 &&
      pagina <= this.totalPaginas &&
      pagina !== this.paginaActual
    ) {
      this.paginaActual = pagina;

      // Si hay un término de búsqueda, filtrar localmente
      if (this.terminoBusqueda && this.terminoBusqueda.trim() !== '') {
        this.mostrarPaginaDeResultadosFiltrados(pagina);
      } else {
        // Si no hay término de búsqueda, mostrar la página correspondiente de todos los usuarios
        this.mostrarPaginaDeTodosLosUsuarios(pagina);
      }
    }
  }

  /**
   * Muestra la página correspondiente de los resultados filtrados
   */
  private mostrarPaginaDeResultadosFiltrados(pagina: number): void {
    // Filtrar todos los usuarios con el término actual
    const terminoLower = this.terminoBusqueda.toLowerCase().trim();

    const usuariosFiltrados = this.todosLosUsuarios.filter((usuario) => {
      const nombreMatch = usuario.nombre
        ? usuario.nombre.toLowerCase().includes(terminoLower)
        : false;

      const usernameMatch = usuario.username
        ? usuario.username.toLowerCase().includes(terminoLower)
        : false;

      const emailMatch = usuario.email
        ? usuario.email.toLowerCase().includes(terminoLower)
        : false;

      const dniMatch = usuario.dni
        ? usuario.dni.toLowerCase().includes(terminoLower)
        : false;

      const idMatch =
        !isNaN(parseInt(terminoLower)) && usuario.id === parseInt(terminoLower);

      return nombreMatch || usernameMatch || emailMatch || dniMatch || idMatch;
    });

    // Actualizar el total de usuarios y páginas
    this.totalUsuarios = usuariosFiltrados.length;
    this.totalPaginas = Math.ceil(this.totalUsuarios / this.tamañoPagina);

    // Asegurarse de que totalPaginas sea al menos 1
    if (this.totalPaginas < 1) {
      this.totalPaginas = 1;
    }

    // Asegurarse de que la página solicitada esté dentro del rango válido
    if (pagina > this.totalPaginas) {
      pagina = this.totalPaginas;
    }

    // Calcular índices para la página solicitada
    const inicio = (pagina - 1) * this.tamañoPagina;
    const fin = Math.min(inicio + this.tamañoPagina, usuariosFiltrados.length);

    // Actualizar la lista de usuarios mostrados
    this.usuarios = usuariosFiltrados.slice(inicio, fin);
    this.usuariosFiltrados = [...this.usuarios];

    console.log(
      `Mostrando página ${pagina} de resultados filtrados. Total páginas: ${this.totalPaginas}`
    );
  }

  /**
   * Muestra la página correspondiente de todos los usuarios
   */
  private mostrarPaginaDeTodosLosUsuarios(pagina: number): void {
    // Actualizar el total de usuarios y páginas
    this.totalUsuarios = this.todosLosUsuarios.length;
    this.totalPaginas = Math.ceil(this.totalUsuarios / this.tamañoPagina);

    // Asegurarse de que totalPaginas sea al menos 1
    if (this.totalPaginas < 1) {
      this.totalPaginas = 1;
    }

    // Asegurarse de que la página solicitada esté dentro del rango válido
    if (pagina > this.totalPaginas) {
      pagina = this.totalPaginas;
    }

    // Calcular índices para la página solicitada
    const inicio = (pagina - 1) * this.tamañoPagina;
    const fin = Math.min(
      inicio + this.tamañoPagina,
      this.todosLosUsuarios.length
    );

    // Actualizar la lista de usuarios mostrados
    this.usuarios = this.todosLosUsuarios.slice(inicio, fin);
    this.usuariosFiltrados = [...this.usuarios];

    console.log(
      `Mostrando página ${pagina} de todos los usuarios. Total páginas: ${this.totalPaginas}`
    );
  }

  /**
   * Selecciona un usuario de la lista
   */
  seleccionarUsuario(usuario: any): void {
    // Construir el nombre completo del usuario
    let nombreCompleto = '';

    if (usuario.nombre && usuario.apellido) {
      // Si tiene nombre y apellido, mostrar ambos
      nombreCompleto = `${usuario.nombre} ${usuario.apellido}`;
    } else if (usuario.nombre) {
      // Si solo tiene nombre, mostrar el nombre
      nombreCompleto = usuario.nombre;
    } else if (usuario.username) {
      // Si no tiene nombre pero tiene username, mostrar el username
      nombreCompleto = usuario.username;
    } else if (usuario.email) {
      // Si no tiene nombre ni username pero tiene email, mostrar el email
      nombreCompleto = usuario.email;
    } else {
      // Si no tiene ninguno de los anteriores, mostrar "Usuario" + ID
      nombreCompleto = `Usuario ${usuario.id}`;
    }

    this.form.patchValue({
      usuarioId: usuario.id,
      nombreUsuario: nombreCompleto,
    });

    console.log('Usuario seleccionado:', {
      id: usuario.id,
      nombre: usuario.nombre,
      apellido: usuario.apellido,
      nombreCompleto: nombreCompleto,
    });

    this.mostrarListaUsuarios = false;
  }

  /**
   * Muestra u oculta la lista de usuarios
   */
  toggleListaUsuarios(): void {
    this.mostrarListaUsuarios = !this.mostrarListaUsuarios;
    if (this.mostrarListaUsuarios && this.usuarios.length === 0) {
      this.cargarUsuarios();
    }
  }

  /**
   * Envía la notificación
   */
  enviarNotificacion(): void {
    if (this.form.invalid) {
      // Marcar todos los campos como tocados para mostrar errores
      Object.keys(this.form.controls).forEach((key) => {
        this.form.get(key)?.markAsTouched();
      });
      return;
    }

    this.enviando = true;
    const formValues = this.form.value;

    // Obtener el ID del usuario actual (remitente)
    const senderId = this.authService.getUserId();

    // Obtener el nombre del usuario desde localStorage
    let senderName = 'Sistema';
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        senderName = user.nombre || 'Sistema';
      }
    } catch (error) {
      console.error('Error al obtener el nombre del usuario:', error);
    }

    // Construir el payload para la notificación
    const payload: any = {
      title: formValues.titulo, // Colocar el título directamente en el campo title
      message: formValues.mensaje,
      senderName: senderName,
      senderId: senderId, // Agregar el ID del remitente
      type: this.convertTipoToNotificationType(formValues.tipo),
      category: formValues.categoria, // Usar la categoría seleccionada por el usuario
      data: {
        link: formValues.enlace || null,
      },
    };

    console.log('Enviando notificación con payload:', payload);

    // Determinar el endpoint y método según el tipo de destinatario
    let endpoint = '';
    let method = 'POST';

    switch (formValues.destinatario) {
      case 'usuario':
        if (formValues.usuarioId) {
          payload.recipientId = parseInt(formValues.usuarioId);
          endpoint = `${environment.url}api/notifications`;
        } else {
          this.notificationService.error('Debe seleccionar un usuario');
          this.enviando = false;
          return;
        }
        break;

      case 'rol':
        if (formValues.rol) {
          payload.role = formValues.rol;
          endpoint = `${environment.url}api/notifications/enviar-por-rol`;
        } else {
          this.notificationService.error('Debe seleccionar un rol');
          this.enviando = false;
          return;
        }
        break;

      case 'sede':
        if (formValues.sede) {
          payload.sedeId = parseInt(formValues.sede);
          endpoint = `${environment.url}api/notifications/enviar-por-sede`;
        } else {
          this.notificationService.error('Debe seleccionar una sede');
          this.enviando = false;
          return;
        }
        break;

      case 'sede-rol':
        if (formValues.sede && formValues.rol) {
          payload.sedeId = parseInt(formValues.sede);
          payload.role = formValues.rol;
          endpoint = `${environment.url}api/notifications/enviar-por-sede-rol`;
        } else {
          this.notificationService.error('Debe seleccionar una sede y un rol');
          this.enviando = false;
          return;
        }
        break;

      default: // 'todos'
        payload.type = 'BROADCAST';
        endpoint = `${environment.url}api/notifications`;
        break;
    }

    console.log('Enviando a endpoint:', endpoint, 'con payload:', payload);

    // Enviar la notificación por HTTP
    this.http.post(endpoint, payload).subscribe({
      next: (response: any) => {
        console.log('Respuesta del servidor:', response);
        this.notificationService.success('Notificación enviada correctamente');
        this.mostrarSweetAlertExito(formValues);
        this.enviando = false;
        this.dialogRef.close(true);
      },
      error: (error) => {
        console.error('Error al enviar notificación:', error);
        this.notificationService.error('Error al enviar la notificación');
        this.enviando = false;
      },
    });
  }

  /**
   * Cierra el diálogo
   */
  cancelar(): void {
    this.dialogRef.close();
  }

  /**
   * Muestra un SweetAlert de éxito personalizado según el tipo de destinatario
   */
  private mostrarSweetAlertExito(formValues: any): void {
    // Obtener el nombre del remitente
    let senderName = 'Sistema';
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        senderName = user.nombre || 'Sistema';
      }
    } catch (error) {
      console.error('Error al obtener el nombre del usuario:', error);
    }

    let titulo = '';
    let mensaje = '';
    let icono: 'success' | 'info' = 'success';
    let iconoEmoji = '';

    // Personalizar el mensaje según el destinatario
    switch (formValues.destinatario) {
      case 'todos':
        titulo = 'Notificación Broadcast Enviada';
        mensaje = `Notificación nueva de ${senderName}: "${formValues.titulo}" enviada a todos los usuarios del sistema.`;
        icono = 'success';
        iconoEmoji = '📢';
        break;

      case 'usuario':
        titulo = 'Notificación Personal Enviada';
        mensaje = `Notificación personal de ${senderName}: "${formValues.titulo}" enviada a ${formValues.nombreUsuario}.`;
        icono = 'info';
        iconoEmoji = '👤';
        break;

      case 'rol':
        titulo = 'Notificación por Rol Enviada';
        mensaje = `Notificación de ${senderName}: "${formValues.titulo}" enviada a todos los usuarios con rol ${formValues.rol}.`;
        icono = 'success';
        iconoEmoji = '🎯';
        break;

      case 'sede':
        const sedeNombre =
          this.sedes.find((s) => s.id == formValues.sede)?.nombre ||
          'sede seleccionada';
        titulo = 'Notificación por Sede Enviada';
        mensaje = `Notificación de ${senderName}: "${formValues.titulo}" enviada a todos los usuarios de ${sedeNombre}.`;
        icono = 'success';
        iconoEmoji = '🏢';
        break;

      case 'sede-rol':
        const sedeNombre2 =
          this.sedes.find((s) => s.id == formValues.sede)?.nombre ||
          'sede seleccionada';
        titulo = 'Notificación por Sede y Rol Enviada';
        mensaje = `Notificación de ${senderName}: "${formValues.titulo}" enviada a usuarios con rol ${formValues.rol} de ${sedeNombre2}.`;
        icono = 'success';
        iconoEmoji = '🎯🏢';
        break;

      default:
        titulo = 'Notificación Enviada';
        mensaje = `Notificación de ${senderName}: "${formValues.titulo}" enviada correctamente.`;
        icono = 'success';
        iconoEmoji = '✅';
        break;
    }

    // Mostrar SweetAlert en la esquina superior derecha con Tailwind CSS
    Swal.fire({
      title: titulo,
      text: mensaje,
      icon: icono,
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 10000, // 10 segundos
      timerProgressBar: true,
      background: '#ffffff',
      color: '#374151',
      customClass: {
        popup: 'swal-notification-toast',
        title: 'swal-notification-title',
      },
      didOpen: (toast) => {
        // Aplicar estilos con Tailwind CSS usando clases
        toast.className +=
          ' mt-16 mr-4 max-w-sm rounded-xl shadow-2xl border border-gray-200 font-sans';

        // Agregar el emoji al título
        const titleElement = toast.querySelector('.swal2-title');
        if (titleElement) {
          titleElement.innerHTML = `${iconoEmoji} ${titulo}`;
          titleElement.className +=
            ' text-base font-semibold text-gray-800 mb-2';
        }

        // Estilizar el contenido
        const contentElement = toast.querySelector('.swal2-html-container');
        if (contentElement) {
          contentElement.className += ' text-sm text-gray-600 leading-relaxed';
        }

        // Estilizar la barra de progreso
        const progressBar = toast.querySelector('.swal2-timer-progress-bar');
        if (progressBar) {
          if (icono === 'success') {
            progressBar.className += ' bg-green-500';
          } else {
            progressBar.className += ' bg-blue-500';
          }
        }

        // Agregar borde lateral colorido
        if (icono === 'success') {
          toast.style.borderLeft = '4px solid #10b981';
        } else {
          toast.style.borderLeft = '4px solid #3b82f6';
        }

        // Configurar z-index alto para aparecer sobre el header
        toast.style.zIndex = '10000';

        // Animación de entrada
        toast.style.animation = 'slideInFromRight 0.4s ease-out';

        // Efecto hover para pausar el timer
        toast.addEventListener('mouseenter', () => {
          Swal.stopTimer();
          toast.style.transform = 'scale(1.02)';
          toast.style.transition = 'transform 0.2s ease';
        });

        toast.addEventListener('mouseleave', () => {
          Swal.resumeTimer();
          toast.style.transform = 'scale(1)';
        });
      },
    });
  }

  /**
   * Carga la lista de roles disponibles
   */
  private cargarRoles(): void {
    this.http.get<string[]>(`${environment.url}api/user/roles`).subscribe({
      next: (roles) => {
        this.roles = roles;
        console.log('Roles cargados:', this.roles);
      },
      error: (err) => {
        console.error('Error al cargar roles:', err);
        this.roles = ['ASESOR', 'COORDINADOR', 'ADMIN', 'BACKOFFICE']; // Fallback
      },
    });
  }

  /**
   * Carga la lista de sedes disponibles
   */
  private cargarSedes(): void {
    this.http.get<any>(`${environment.url}api/sedes/activas`).subscribe({
      next: (respuesta) => {
        if (respuesta && respuesta.rpta === 1 && respuesta.data) {
          this.sedes = respuesta.data;
          console.log('Sedes cargadas:', this.sedes);
        } else {
          console.warn('Respuesta de sedes no válida:', respuesta);
          this.sedes = [];
        }
      },
      error: (err) => {
        console.error('Error al cargar sedes:', err);
        this.sedes = [];
      },
    });
  }

  /**
   * Convierte el tipo de notificación del frontend al formato del backend
   */
  private convertTipoToNotificationType(tipo: TipoNotificacion): string {
    switch (tipo) {
      case TipoNotificacion.INFO:
        return 'INFO';
      case TipoNotificacion.EXITO:
        return 'INFO';
      case TipoNotificacion.ADVERTENCIA:
        return 'ALERT';
      case TipoNotificacion.ERROR:
        return 'ALERT';
      case TipoNotificacion.SISTEMA:
        return 'SYSTEM';
      default:
        return 'INFO';
    }
  }
}
