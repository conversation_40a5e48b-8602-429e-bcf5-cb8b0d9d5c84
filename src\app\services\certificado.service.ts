import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, from } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { AngularFireStorage } from '@angular/fire/compat/storage';

import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';
import { 
  Certificado, 
  CertificadoCreateRequest, 
  ProgresoDetallado, 
  UsuarioElegible 
} from '@app/models/backend/certificado/certificado.model';

@Injectable({
  providedIn: 'root'
})
export class CertificadoService {
  private baseUrl = environment.url + 'api/certificados';

  constructor(
    private http: HttpClient,
    private storage: AngularFireStorage
  ) {}

  /**
   * Crear un nuevo certificado
   */
  crearCertificado(certificado: CertificadoCreateRequest): Observable<GenericResponse<Certificado>> {
    return this.http.post<GenericResponse<Certificado>>(this.baseUrl, certificado);
  }

  /**
   * Crear certificado con imagen generada
   */
  crearCertificadoConImagen(
    certificado: CertificadoCreateRequest,
    usuarioNombre: string,
    usuarioApellido: string,
    cursoNombre: string
  ): Observable<GenericResponse<Certificado>> {
    // Primero generar la imagen del certificado
    return this.generarImagenCertificado(usuarioNombre, usuarioApellido, cursoNombre, certificado.horasCurso)
      .pipe(
        switchMap(certificadoUrl => {
          // Crear el certificado en el backend con la URL de la imagen
          const certificadoConUrl = { ...certificado, certificadoUrl };
          return this.http.post<GenericResponse<Certificado>>(this.baseUrl, certificadoConUrl);
        })
      );
  }

  /**
   * Obtener certificado por ID
   */
  obtenerCertificado(id: number): Observable<GenericResponse<Certificado>> {
    return this.http.get<GenericResponse<Certificado>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Obtener certificados de un usuario
   */
  obtenerCertificadosPorUsuario(usuarioId: number): Observable<GenericResponse<Certificado[]>> {
    return this.http.get<GenericResponse<Certificado[]>>(`${this.baseUrl}/usuario/${usuarioId}`);
  }

  /**
   * Obtener certificados de un curso
   */
  obtenerCertificadosPorCurso(cursoId: number): Observable<GenericResponse<Certificado[]>> {
    return this.http.get<GenericResponse<Certificado[]>>(`${this.baseUrl}/curso/${cursoId}`);
  }

  /**
   * Verificar elegibilidad para certificado
   */
  verificarElegibilidad(usuarioId: number, cursoId: number): Observable<GenericResponse<ProgresoDetallado>> {
    return this.http.get<GenericResponse<ProgresoDetallado>>(`${this.baseUrl}/verificar/${usuarioId}/${cursoId}`);
  }

  /**
   * Obtener usuarios elegibles para certificado de un curso
   */
  obtenerUsuariosElegibles(cursoId: number): Observable<GenericResponse<UsuarioElegible[]>> {
    return this.http.get<GenericResponse<UsuarioElegible[]>>(`${this.baseUrl}/elegibles/${cursoId}`);
  }

  /**
   * Obtener certificados con paginación
   */
  obtenerCertificadosPaginados(
    page: number = 0,
    size: number = 10,
    sortBy: string = 'fechaCreacion',
    sortDir: string = 'desc',
    search?: string,
    estado?: string
  ): Observable<GenericResponse<any>> {
    let params = `page=${page}&size=${size}&sortBy=${sortBy}&sortDir=${sortDir}`;
    if (search) params += `&search=${encodeURIComponent(search)}`;
    if (estado) params += `&estado=${estado}`;
    
    return this.http.get<GenericResponse<any>>(`${this.baseUrl}?${params}`);
  }

  /**
   * Obtener certificado por código
   */
  obtenerCertificadoPorCodigo(codigo: string): Observable<GenericResponse<Certificado>> {
    return this.http.get<GenericResponse<Certificado>>(`${this.baseUrl}/codigo/${codigo}`);
  }

  /**
   * Eliminar certificado
   */
  eliminarCertificado(id: number): Observable<GenericResponse<string>> {
    return this.http.delete<GenericResponse<string>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Generar imagen del certificado y subirla a Firebase
   */
  private generarImagenCertificado(
    nombreUsuario: string,
    apellidoUsuario: string,
    nombreCurso: string,
    horasCurso: number
  ): Observable<string> {
    return new Observable<string>(observer => {
      // Crear un canvas para generar el certificado
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        observer.error('No se pudo crear el contexto del canvas');
        return;
      }

      // Configurar el tamaño del canvas (formato A4 horizontal)
      canvas.width = 1200;
      canvas.height = 850;

      // Cargar la imagen de fondo del certificado
      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => {
        // Dibujar la imagen de fondo
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        // Configurar el texto
        ctx.textAlign = 'center';
        ctx.fillStyle = '#2c3e50';

        // Nombre del usuario (más grande y prominente)
        ctx.font = 'bold 48px Poppins, Arial, sans-serif';
        ctx.fillText(`${nombreUsuario} ${apellidoUsuario}`, canvas.width / 2, 400);

        // Texto del curso
        ctx.font = '32px Poppins, Arial, sans-serif';
        ctx.fillText(`Por haber completado satisfactoriamente el curso de`, canvas.width / 2, 480);

        // Nombre del curso
        ctx.font = 'bold 36px Poppins, Arial, sans-serif';
        ctx.fillText(nombreCurso, canvas.width / 2, 540);

        // Fecha y horas
        const fechaActual = new Date().toLocaleDateString('es-ES', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
        
        ctx.font = '24px Poppins, Arial, sans-serif';
        ctx.fillText(`Fecha: ${fechaActual}`, canvas.width / 2, 620);
        ctx.fillText(`Duración: ${horasCurso} horas académicas`, canvas.width / 2, 660);

        // Convertir canvas a blob
        canvas.toBlob(blob => {
          if (!blob) {
            observer.error('No se pudo generar la imagen del certificado');
            return;
          }

          // Subir a Firebase
          const fileName = `certificado_${Date.now()}_${nombreUsuario}_${apellidoUsuario}.png`;
          const filePath = `certificados/${fileName}`;
          const fileRef = this.storage.ref(filePath);
          const task = this.storage.upload(filePath, blob);

          task.then(() => {
            fileRef.getDownloadURL().subscribe(
              url => {
                observer.next(url);
                observer.complete();
              },
              error => observer.error(error)
            );
          }).catch(error => observer.error(error));
        }, 'image/png');
      };

      img.onerror = () => {
        observer.error('No se pudo cargar la imagen del certificado');
      };

      // Cargar la imagen desde assets
      img.src = '/assets/certificado.png';
    });
  }

  /**
   * Descargar certificado como PDF
   */
  descargarCertificadoPDF(certificado: Certificado): void {
    if (certificado.certificadoUrl) {
      // Crear un enlace temporal para descargar
      const link = document.createElement('a');
      link.href = certificado.certificadoUrl;
      link.download = `Certificado_${certificado.usuarioNombre}_${certificado.usuarioApellido}_${certificado.cursoNombre}.png`;
      link.click();
    }
  }
}
